<script setup lang="ts">
import { RouterView } from 'vue-router'
import { ConfigProvider } from 'ant-design-vue'
import { onMounted } from 'vue'
import { useSettingsStore } from '@/stores/settingsStore'
import zhCN from 'ant-design-vue/es/locale/zh_CN'

const settingsStore = useSettingsStore()

onMounted(() => {
  // 加载保存的设置
  settingsStore.loadSettings()
})
</script>

<template>
  <ConfigProvider
    :theme="settingsStore.currentTheme"
    :locale="zhCN"
  >
    <RouterView />
  </ConfigProvider>
</template>

<style scoped></style>
