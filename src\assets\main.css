/* 引入字体 */
@font-face {
  font-family: 'Running Regular';
  src: url('./fonts/running-regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

/* 全局字体设置 */
* {
  font-family: 'Running Regular', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
}

/* 游戏主题色彩变量 */
:root {
  --primary-color: #722ed1;
  --primary-color-hover: #9254de;
  --primary-color-active: #531dab;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  --info-color: #1890ff;
  
  /* 游戏特色颜色 */
  --nectar-gold: #d4af37;
  --nectar-purple: #8b5cf6;
  --nectar-pink: #ec4899;
  --nectar-blue: #3b82f6;
  
  /* 背景色 */
  --bg-primary: #f0f2f5;
  --bg-secondary: #ffffff;
  --bg-tertiary: #fafafa;
}

/* 基础样式重置 */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  background-color: var(--bg-primary);
}

#app {
  height: 100vh;
  /* overflow: hidden; */
}

/* 移动端适配 */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }
  
  .ant-layout-sider {
    width: 200px !important;
    min-width: 200px !important;
  }
}

/* 游戏风格的卡片样式 */
.game-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  background: var(--bg-secondary);
}

.game-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* 角色头像样式 */
.character-avatar {
  border-radius: 8px;
  border: 2px solid var(--nectar-gold);
  transition: all 0.3s ease;
}

.character-avatar:hover {
  border-color: var(--nectar-purple);
  transform: scale(1.05);
}

/* 按钮样式增强 */
.ant-btn-primary {
  background: linear-gradient(135deg, var(--nectar-purple), var(--nectar-pink));
  border: none;
  border-radius: 8px;
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, var(--nectar-pink), var(--nectar-purple));
}

/* 工坊入口按钮样式 */
.workshop-btn {
  height: 80px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--nectar-gold), var(--nectar-purple));
  border: none;
  color: white;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.workshop-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  background: linear-gradient(135deg, var(--nectar-purple), var(--nectar-gold));
}

/* 时间显示样式 */
.time-display {
  background: linear-gradient(135deg, var(--nectar-blue), var(--nectar-purple));
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: bold;
  text-align: center;
}

/* 派遣槽位样式 */
.dispatch-slot {
  border: 2px dashed var(--nectar-gold);
  border-radius: 8px;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  background: rgba(212, 175, 55, 0.1);
}

.dispatch-slot.occupied {
  border-style: solid;
  background: var(--bg-secondary);
}

.dispatch-slot:hover {
  border-color: var(--nectar-purple);
  background: rgba(139, 92, 246, 0.1);
}
