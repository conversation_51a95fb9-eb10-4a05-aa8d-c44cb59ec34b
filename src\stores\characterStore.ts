import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import charactersNameData from '@/assets/data/characters/NameList.json'
import portraitsData from '@/assets/graphics/portraits/characters_portraits.json'
import { useSkillStore } from './skillStore'

export type CharacterType = 'Normal' | 'Rare' | 'Special'
export type ActivityType = 'Idle' | 'Producing' | 'Performing' | 'Resting'
export type CharacterStatus = 'Normal' | 'Tired' | 'Overworked' | 'Resting' | 'Exiled'

export interface DispatchRecord {
  workshopType: string // 工坊类型
  activity: ActivityType // 活动类型
  startDay: number // 开始天数
  endDay?: number // 结束天数（如果已完成）
  duration: number // 持续时间（天数）
  rewards?: {
    experience: number
    materials?: Record<string, number>
    money?: number
  }
}

export interface Character {
  id: string
  name: string
  portrait: string
  type: CharacterType
  attributes: {
    energy: number
    maxEnergy: number
    isAssigned: boolean
    assignedWorkshop?: string // 派遣的工坊类型
    skills: string[] // 技能ID列表
    efficiency: number // 生产效率基础值
  }
  currentActivity: ActivityType
  mood: number // 心情值 0-100 (整数)
  stamina: number // 体力值 0-100 (整数)
  experience: number // 经验值 (整数)
  level: number // 等级 1-10
  restDays: number // 连续未休息天数
  status: CharacterStatus // 当前状态
  lastRestDay: number // 上次休息的天数
  meetDate: string // 邂逅日期 (ISO格式)
  meetDay: number // 邂逅的游戏天数
  dispatchHistory: DispatchRecord[] // 派遣历史记录
}

export const useCharacterStore = defineStore('character', () => {
  // 角色列表
  const characters = ref<Character[]>([])

  // 已使用的名字列表
  const usedNames = ref<Set<string>>(new Set())

  // 计算属性：可用角色（未被派遣的）
  const availableCharacters = computed(() => {
    return characters.value.filter(char => !char.attributes.isAssigned)
  })

  // 计算属性：按类型分组的角色
  const charactersByType = computed(() => {
    return {
      Normal: characters.value.filter(char => char.type === 'Normal'),
      Rare: characters.value.filter(char => char.type === 'Rare'),
      Special: characters.value.filter(char => char.type === 'Special'),
    }
  })

  // 生成随机角色ID
  function generateCharacterId(): string {
    return 'char_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
  }

  // 获取随机未使用的名字
  function getRandomUnusedName(): string {
    const availableNames = charactersNameData.charactersName.filter(
      name => !usedNames.value.has(name)
    )

    if (availableNames.length === 0) {
      // 如果所有名字都用完了，重置已使用名字列表
      usedNames.value.clear()
      return charactersNameData.charactersName[
        Math.floor(Math.random() * charactersNameData.charactersName.length)
      ]
    }

    const randomName = availableNames[Math.floor(Math.random() * availableNames.length)]
    usedNames.value.add(randomName)
    return randomName
  }

  // 获取随机头像
  function getRandomPortrait(): string {
    const randomIndex = Math.floor(Math.random() * portraitsData.length)
    return portraitsData[randomIndex]
  }

  // 生成角色属性
  function generateCharacterAttributes(type: CharacterType) {
    const skillStore = useSkillStore()
    let baseEfficiency = 1.0
    let baseEnergy = 100

    switch (type) {
      case 'Rare':
        baseEfficiency = 1.2 + Math.random() * 0.3 // 1.2-1.5
        baseEnergy = 120
        break
      case 'Special':
        baseEfficiency = 1.5 + Math.random() * 0.5 // 1.5-2.0
        baseEnergy = 150
        break
      default: // Normal
        baseEfficiency = 0.8 + Math.random() * 0.4 // 0.8-1.2
        break
    }

    return {
      energy: baseEnergy,
      maxEnergy: baseEnergy,
      isAssigned: false,
      skills: generateRandomSkills(type, skillStore),
      efficiency: Math.round(baseEfficiency * 10) / 10 // 保留一位小数
    }
  }

  // 生成随机技能
  function generateRandomSkills(type: CharacterType, skillStore: any): string[] {
    const skills: string[] = []

    switch (type) {
      case 'Normal':
        // Normal角色：小概率附带1个常见技能
        if (Math.random() < 0.3) { // 30%概率
          const commonSkill = skillStore.getRandomSkill('Common')
          if (commonSkill) skills.push(commonSkill.id)
        }
        break

      case 'Rare':
        // Rare角色：必携带一个常见技能，小概率再附带一个稀有技能
        const commonSkill = skillStore.getRandomSkill('Common')
        if (commonSkill) skills.push(commonSkill.id)

        if (Math.random() < 0.4) { // 40%概率获得稀有技能
          const rareSkill = skillStore.getRandomSkill('Rare')
          if (rareSkill) skills.push(rareSkill.id)
        }
        break

      case 'Special':
        // Special角色：必携带一个常见技能和一个稀有技能
        const specialCommonSkill = skillStore.getRandomSkill('Common')
        const specialRareSkill = skillStore.getRandomSkill('Rare')
        if (specialCommonSkill) skills.push(specialCommonSkill.id)
        if (specialRareSkill) skills.push(specialRareSkill.id)
        break
    }

    return skills
  }

  // 创建新角色
  function createCharacter(type: CharacterType = 'Normal', gameDay?: number): Character {
    // 根据角色类型设置初始属性
    let baseMood = 60
    let baseStamina = 70

    switch (type) {
      case 'Rare':
        baseMood = 70
        baseStamina = 80
        break
      case 'Special':
        baseMood = 80
        baseStamina = 90
        break
    }

    const currentGameDay = gameDay || 1 // 如果没有传入游戏天数，默认为第1天

    // 使用游戏时间计算邂逅日期（游戏开始日期：2025年6月1日）
    const gameStartDate = new Date('2025-06-01')
    const meetDate = new Date(gameStartDate)
    meetDate.setDate(gameStartDate.getDate() + currentGameDay - 1)

    const character: Character = {
      id: generateCharacterId(),
      name: getRandomUnusedName(),
      portrait: getRandomPortrait(),
      type,
      attributes: generateCharacterAttributes(type),
      currentActivity: 'Idle',
      mood: Math.floor(baseMood + Math.random() * 20), // 整数值
      stamina: Math.floor(baseStamina + Math.random() * 20), // 整数值
      experience: 0,
      level: 1,
      restDays: 0,
      status: 'Normal',
      lastRestDay: 0,
      meetDate: meetDate.toISOString(),
      meetDay: currentGameDay,
      dispatchHistory: [] // 初始化空的派遣历史
    }

    characters.value.push(character)
    return character
  }

  // 根据ID查找角色
  function getCharacterById(id: string): Character | undefined {
    return characters.value.find(char => char.id === id)
  }

  // 派遣角色
  function assignCharacter(characterId: string, activity: ActivityType, workshopType?: string, currentDay?: number): boolean {
    const character = getCharacterById(characterId)
    if (!character || character.attributes.isAssigned) {
      return false
    }

    character.attributes.isAssigned = true
    character.attributes.assignedWorkshop = workshopType
    character.currentActivity = activity

    // 记录派遣历史
    if (workshopType && currentDay) {
      const dispatchRecord: DispatchRecord = {
        workshopType,
        activity,
        startDay: currentDay,
        duration: 1 // 默认1天
      }
      character.dispatchHistory.push(dispatchRecord)
    }

    return true
  }

  // 取消派遣
  function unassignCharacter(characterId: string, currentDay?: number): boolean {
    const character = getCharacterById(characterId)
    if (!character) return false

    // 更新最后一条派遣记录的结束时间
    if (character.dispatchHistory.length > 0 && currentDay) {
      const lastRecord = character.dispatchHistory[character.dispatchHistory.length - 1]
      if (!lastRecord.endDay) {
        lastRecord.endDay = currentDay
        lastRecord.duration = currentDay - lastRecord.startDay + 1
      }
    }

    character.attributes.isAssigned = false
    character.attributes.assignedWorkshop = undefined
    character.currentActivity = 'Idle'
    return true
  }

  // 恢复角色体力和心情
  function restoreCharacter(characterId: string, stamina: number = 20, mood: number = 10) {
    const character = getCharacterById(characterId)
    if (!character) return false

    character.stamina = Math.min(character.stamina + stamina, 100)
    character.mood = Math.min(character.mood + mood, 100)
    return true
  }

  // 消耗角色体力和心情
  function consumeCharacterStats(characterId: string, stamina: number = 10, mood: number = 5) {
    const character = getCharacterById(characterId)
    if (!character) return false

    character.stamina = Math.max(character.stamina - stamina, 0)
    character.mood = Math.max(character.mood - mood, 0)
    return true
  }

  // 增加角色经验
  function addExperience(characterId: string, exp: number) {
    const character = getCharacterById(characterId)
    if (!character || character.level >= 10) return false

    character.experience += Math.floor(exp) // 确保经验值为整数

    // 检查是否升级 (每级需要100点经验)
    while (character.experience >= 100 && character.level < 10) {
      character.level += 1
      character.experience -= 100

      // 升级时恢复至最初的体力值和心情值
      const initialStats = getInitialStats(character.type)
      character.stamina = initialStats.stamina
      character.mood = initialStats.mood
      character.attributes.maxEnergy += 10
      character.attributes.efficiency = Math.round((character.attributes.efficiency + 0.1) * 10) / 10

      // Special角色5级后解锁额外稀有技能
      if (character.type === 'Special' && character.level === 5) {
        const skillStore = useSkillStore()
        const additionalRareSkill = skillStore.getRandomSkill('Rare')
        if (additionalRareSkill && !character.attributes.skills.includes(additionalRareSkill.id)) {
          character.attributes.skills.push(additionalRareSkill.id)
        }
      }
    }

    return true
  }

  // 获取角色类型的初始属性
  function getInitialStats(type: CharacterType) {
    switch (type) {
      case 'Rare':
        return { stamina: 80, mood: 70 }
      case 'Special':
        return { stamina: 90, mood: 80 }
      default:
        return { stamina: 70, mood: 60 }
    }
  }

  // 更新角色状态
  function updateCharacterStatus(characterId: string) {
    const character = getCharacterById(characterId)
    if (!character) return false

    // 根据条件更新状态
    if (character.status === 'Exiled') {
      return false // 流放状态不能改变
    }

    if (character.status === 'Resting') {
      return false // 休息状态由其他方法管理
    }

    // 检查是否需要进入Overworked状态
    if (character.restDays >= 3 || (character.mood <= 30 && character.stamina <= 30)) {
      character.status = 'Overworked'
      character.attributes.isAssigned = false
      character.currentActivity = 'Idle'
      return true
    }

    // 检查是否进入Tired状态
    if (character.restDays >= 2 || character.mood <= 50 || character.stamina <= 50) {
      character.status = 'Tired'
      return true
    }

    // 检查是否恢复Normal状态
    if (character.mood > 50 && character.stamina > 50 && character.restDays === 0) {
      character.status = 'Normal'
      return true
    }

    return false
  }

  // 每日恢复处理
  function dailyRecovery(currentDay: number) {
    characters.value.forEach(character => {
      // 体力每天自动回复10点
      const skillStore = useSkillStore()
      let staminaRecovery = 10

      // 计算技能加成
      const skillBonus = skillStore.calculateSkillEffect(character.attributes.skills, 'stamina_recovery', 'daily')
      staminaRecovery += skillBonus

      character.stamina = Math.min(character.stamina + staminaRecovery, 100)

      // 更新连续未休息天数
      if (character.currentActivity !== 'Resting' && character.status !== 'Resting') {
        character.restDays += 1
      }

      // 更新角色状态
      updateCharacterStatus(character.id)

      // 检查是否需要流放
      if (character.status === 'Overworked' && (currentDay - character.lastRestDay) > 3) {
        character.status = 'Exiled'
        character.attributes.isAssigned = false
        character.currentActivity = 'Idle'
      }
    })
  }

  // 角色休息
  function restCharacter(characterId: string, currentDay: number): boolean {
    const character = getCharacterById(characterId)
    if (!character || character.status === 'Exiled') return false

    character.status = 'Resting'
    character.currentActivity = 'Resting'
    character.attributes.isAssigned = false
    character.restDays = 0
    character.lastRestDay = currentDay

    // 恢复体力与心情+10
    character.stamina = Math.min(character.stamina + 10, 100)
    character.mood = Math.min(character.mood + 10, 100)

    return true
  }

  // 结束所有派遣（在推进到夜晚时调用）
  function endAllDispatches(currentDay: number): number {
    let endedCount = 0

    characters.value.forEach(character => {
      if (character.attributes.isAssigned) {
        // 结束派遣记录
        if (character.dispatchHistory.length > 0) {
          const lastRecord = character.dispatchHistory[character.dispatchHistory.length - 1]
          if (!lastRecord.endDay) {
            lastRecord.endDay = currentDay
            lastRecord.duration = currentDay - lastRecord.startDay + 1
          }
        }

        // 重置角色状态
        character.attributes.isAssigned = false
        character.attributes.assignedWorkshop = undefined
        character.currentActivity = 'Idle'
        endedCount++
      }
    })

    return endedCount
  }

  // 双修恢复
  function dualCultivation(characterId: string): boolean {
    const character = getCharacterById(characterId)
    if (!character || character.status === 'Exiled') return false

    const skillStore = useSkillStore()
    let staminaRecovery = 30
    let moodRecovery = 30

    // 计算技能加成
    const staminaBonus = skillStore.calculateSkillEffect(character.attributes.skills, 'stamina_recovery', 'dual_cultivation')
    const moodBonus = skillStore.calculateSkillEffect(character.attributes.skills, 'mood_recovery', 'dual_cultivation')

    staminaRecovery += staminaBonus
    moodRecovery += Math.floor(moodRecovery * moodBonus / 100) // 百分比加成

    character.stamina = Math.min(character.stamina + staminaRecovery, 100)
    character.mood = Math.min(character.mood + moodRecovery, 100)

    // 更新状态
    updateCharacterStatus(character.id)

    return true
  }

  // 群修恢复
  function groupCultivation(characterIds: string[]): boolean {
    const skillStore = useSkillStore()
    let maxParticipants = 3 // 基础上限

    // 检查是否有群修引领技能
    characterIds.forEach(id => {
      const character = getCharacterById(id)
      if (character) {
        const leadershipBonus = skillStore.calculateSkillEffect(character.attributes.skills, 'special', 'group_cultivation')
        maxParticipants += leadershipBonus
      }
    })

    // 限制参与人数
    const participants = characterIds.slice(0, maxParticipants)

    participants.forEach(id => {
      const character = getCharacterById(id)
      if (character && character.status !== 'Exiled') {
        character.stamina = Math.min(character.stamina + 20, 100)
        character.mood = Math.min(character.mood + 20, 100)
        updateCharacterStatus(character.id)
      }
    })

    return true
  }

  // 初始化默认角色
  function initializeDefaultCharacters(gameDay: number = 1) {
    if (characters.value.length === 0) {
      // 创建3个初始角色
      createCharacter('Normal', gameDay)
      createCharacter('Normal', gameDay)
      createCharacter('Rare', gameDay)
    }
  }

  // 保存角色数据
  function saveCharacters() {
    const data = {
      characters: characters.value,
      usedNames: Array.from(usedNames.value)
    }
    localStorage.setItem('nectar-game-characters', JSON.stringify(data))
  }

  // 加载角色数据
  function loadCharacters(): boolean {
    try {
      const saved = localStorage.getItem('nectar-game-characters')
      if (saved) {
        const data = JSON.parse(saved)
        characters.value = data.characters || []
        usedNames.value = new Set(data.usedNames || [])
        return true
      }
    } catch (error) {
      console.error('Failed to load characters:', error)
    }
    return false
  }

  // 重置角色数据
  function resetCharacters(gameDay: number = 1) {
    characters.value = []
    usedNames.value.clear()
    localStorage.removeItem('nectar-game-characters')
    initializeDefaultCharacters(gameDay)
  }

  return {
    // 状态
    characters,
    usedNames,

    // 计算属性
    availableCharacters,
    charactersByType,

    // 方法
    createCharacter,
    getCharacterById,
    assignCharacter,
    unassignCharacter,
    restoreCharacter,
    consumeCharacterStats,
    addExperience,
    updateCharacterStatus,
    dailyRecovery,
    restCharacter,
    endAllDispatches,
    dualCultivation,
    groupCultivation,
    initializeDefaultCharacters,
    saveCharacters,
    loadCharacters,
    resetCharacters,
  }
})
