<template>
  <div class="character-card" :class="{
    'selected': selected,
    'selectable': selectable,
    'disabled': disabled,
    [`type-${character.type.toLowerCase()}`]: true,
    [`status-${character.status.toLowerCase()}`]: true
  }" @click="handleCardClick">
    <!-- 选中状态指示器 -->
    <div v-if="selected" class="selection-indicator">
      <CheckOutlined />
    </div>

    <!-- 角色头像区域 -->
    <div class="character-avatar-section">
      <a-avatar :src="`/src/assets/graphics/portraits/${character.portrait}`" :size="64" class="character-avatar">
        {{ character.name.charAt(0) }}
      </a-avatar>
      <h4 class="character-name">{{ character.name }}</h4>

      <!-- 等级标识 -->
      <div class="level-indicator">
        Lv.{{ character.level }}
      </div>
    </div>

    <!-- 角色信息区域 -->
    <div class="character-info-section">
      <!-- 角色名称和类型 -->
      <div class="character-header">
        <!-- 状态徽章 -->
        <a-badge :color="getStatusColor(character.status)" :text="getStatusText(character.status)"
          class="status-badge" />
        <a-tag :color="getTypeColor(character.type)" size="small" class="type-tag">
          {{ getTypeText(character.type) }}
        </a-tag>
      </div>

      <!-- 属性条 -->
      <div class="character-attributes">
        <div class="attribute-item">
          <span class="attribute-label">体力</span>
          <a-progress :percent="character.stamina" :stroke-color="getStaminaColor(character.stamina)" :show-info="false"
            size="small" class="attribute-progress" />
          <span class="attribute-value">{{ character.stamina }}</span>
        </div>

        <div class="attribute-item">
          <span class="attribute-label">心情</span>
          <a-progress :percent="character.mood" :stroke-color="getMoodColor(character.mood)" :show-info="false"
            size="small" class="attribute-progress" />
          <span class="attribute-value">{{ character.mood }}</span>
        </div>
      </div>

      <!-- 技能展示 -->
      <div class="character-skills" v-if="character.attributes.skills.length > 0">
        <div class="skills-header">
          <span class="skills-label">技能</span>
        </div>
        <div class="skills-list">
          <a-tag v-for="skillId in character.attributes.skills.slice(0, 2)" :key="skillId"
            :color="getSkillColor(skillId)" size="small" class="skill-tag">
            {{ getSkillName(skillId) }}
          </a-tag>
          <span v-if="character.attributes.skills.length > 2" class="more-skills">
            +{{ character.attributes.skills.length - 2 }}
          </span>
        </div>
      </div>

      <!-- 当前活动状态 -->
      <div class="character-activity" v-if="character.currentActivity !== 'Idle'">
        <a-tag color="processing" size="small">
          {{ getActivityText(character.currentActivity) }}
        </a-tag>
      </div>

      <!-- 派遣状态 -->
      <div class="dispatch-status" v-if="character.attributes.isAssigned && character.attributes.assignedWorkshop">
        <div class="dispatch-info">
          <span class="dispatch-label">派遣至:</span>
          <a-tag color="blue" size="small" class="workshop-tag">
            {{ getWorkshopName(character.attributes.assignedWorkshop) }}
          </a-tag>
        </div>
        <div class="dispatch-meta">
          <span class="dispatch-time">进行中</span>
        </div>
      </div>
    </div>

    <!-- 详情按钮 -->
    <div class="character-actions">
      <a-button type="text" size="small" @click.stop="showDetails = true" class="details-btn">
        详情
      </a-button>
    </div>

    <!-- 角色详情弹窗 -->
    <a-modal v-model:open="showDetails" :title="`${character.name} - 详细信息`" :footer="null" width="500px" centered>
      <CharacterDetails :character="character" />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { CheckOutlined } from '@ant-design/icons-vue'
import type { Character } from '@/stores/characterStore'
import { useSkillStore } from '@/stores/skillStore'
import CharacterDetails from './CharacterDetails.vue'

interface Props {
  character: Character
  selected?: boolean
  selectable?: boolean
  disabled?: boolean
}

interface Emits {
  (e: 'select', character: Character): void
  (e: 'click', character: Character): void
}

const props = withDefaults(defineProps<Props>(), {
  selected: false,
  selectable: false,
  disabled: false
})

const emit = defineEmits<Emits>()

const skillStore = useSkillStore()
const showDetails = ref(false)

// 处理卡片点击
const handleCardClick = () => {
  if (props.disabled) return

  if (props.selectable) {
    emit('select', props.character)
  } else {
    emit('click', props.character)
  }
}

// 获取状态文本
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    'Normal': '正常',
    'Tired': '疲惫',
    'Overworked': '过劳',
    'Resting': '休息',
    'Exiled': '流放'
  }
  return statusMap[status] || status
}

// 获取类型文本
const getTypeText = (type: string): string => {
  const typeMap: Record<string, string> = {
    'Normal': '普通',
    'Rare': '稀有',
    'Special': '特殊'
  }
  return typeMap[type] || type
}

// 获取技能名称
const getSkillName = (skillId: string): string => {
  const skill = skillStore.getSkillById(skillId)
  return skill ? skill.name : skillId
}

// 获取技能颜色
const getSkillColor = (skillId: string): string => {
  const skill = skillStore.getSkillById(skillId)
  if (!skill) return 'default'
  return skill.rarity === 'Rare' ? 'purple' : 'blue'
}

// 获取状态颜色
const getStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    'Normal': 'green',
    'Tired': 'orange',
    'Overworked': 'red',
    'Resting': 'blue',
    'Exiled': 'default'
  }
  return colorMap[status] || 'default'
}

// 获取类型颜色
const getTypeColor = (type: string): string => {
  const colorMap: Record<string, string> = {
    'Normal': 'default',
    'Rare': 'purple',
    'Special': 'gold'
  }
  return colorMap[type] || 'default'
}

// 获取体力颜色
const getStaminaColor = (stamina: number): string => {
  if (stamina >= 70) return '#52c41a'
  if (stamina >= 40) return '#faad14'
  return '#ff4d4f'
}

// 获取心情颜色
const getMoodColor = (mood: number): string => {
  if (mood >= 70) return '#52c41a'
  if (mood >= 40) return '#faad14'
  return '#ff4d4f'
}

// 获取活动文本
const getActivityText = (activity: string): string => {
  const activityMap: Record<string, string> = {
    'Idle': '空闲',
    'Producing': '生产中',
    'Resting': '休息中',
    'Training': '训练中'
  }
  return activityMap[activity] || activity
}

// 获取工坊名称
const getWorkshopName = (workshopType: string): string => {
  const workshopMap: Record<string, string> = {
    'hall': '会客大厅',
    'holy-water': '圣水工坊',
    'honey': '蜜酿工坊',
    'milk': '乳液工坊',
    'nectar': '琼浆工坊'
  }
  return workshopMap[workshopType] || workshopType
}
</script>

<style scoped>
.character-card {
  position: relative;
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  border: 2px solid transparent;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.character-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.character-card.selectable:hover {
  border-color: var(--nectar-purple);
}

.character-card.selected {
  border-color: var(--nectar-purple);
  background: linear-gradient(135deg, #f6f5ff 0%, #fff 100%);
}

.character-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  filter: grayscale(0.3);
}

.character-card.disabled:hover {
  transform: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 类型样式 */
.character-card.type-normal {
  border-left: 4px solid #d9d9d9;
}

.character-card.type-rare {
  border-left: 4px solid #722ed1;
}

.character-card.type-special {
  border-left: 4px solid #faad14;
}

/* 状态样式 */
.character-card.status-overworked {
  background: linear-gradient(135deg, #fff2f0 0%, #fff 100%);
}

.character-card.status-tired {
  background: linear-gradient(135deg, #fff7e6 0%, #fff 100%);
}

.character-card.status-resting {
  background: linear-gradient(135deg, #f6ffed 0%, #fff 100%);
}

.character-card.status-exiled {
  background: linear-gradient(135deg, #f5f5f5 0%, #fff 100%);
}

/* 选中指示器 */
.selection-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  background: var(--nectar-purple);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  z-index: 2;
}

/* 头像区域 */
.character-avatar-section {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.character-avatar {
  border: 3px solid #f0f0f0;
  transition: border-color 0.3s ease;
}

.character-card.type-rare .character-avatar {
  border-color: #722ed1;
}

.character-card.type-special .character-avatar {
  border-color: #faad14;
}

.status-badge {
  font-size: 11px;
}

.level-indicator {
  background: var(--nectar-purple);
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: bold;
}

/* 信息区域 */
.character-info-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.character-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.character-name {
  margin: 0;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.type-tag {
  font-weight: bold;
  border-radius: 8px;
}

/* 属性条 */
.character-attributes {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.attribute-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.attribute-label {
  font-size: 11px;
  color: #666;
  min-width: 28px;
  font-weight: 500;
}

.attribute-progress {
  flex: 1;
}

.attribute-value {
  font-size: 11px;
  color: #333;
  font-weight: bold;
  min-width: 24px;
  text-align: right;
}

/* 技能区域 */
.character-skills {
  margin-top: 4px;
}

.skills-header {
  margin-bottom: 4px;
}

.skills-label {
  font-size: 11px;
  color: #666;
  font-weight: 500;
}

.skills-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.skill-tag {
  font-size: 10px;
  border-radius: 6px;
  margin: 0;
}

.more-skills {
  font-size: 10px;
  color: #999;
  font-weight: 500;
}

/* 活动状态 */
.character-activity {
  margin-top: 4px;
}

/* 派遣状态 */
.dispatch-status {
  margin-top: 8px;
  padding: 8px;
  background: #f0f8ff;
  border-radius: 6px;
  border-left: 3px solid #1890ff;
}

.dispatch-info {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 4px;
}

.dispatch-label {
  font-size: 11px;
  color: #666;
  font-weight: 500;
}

.workshop-tag {
  font-size: 10px !important;
}

.dispatch-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dispatch-time {
  font-size: 10px;
  color: #1890ff;
  font-weight: 500;
}

/* 操作按钮 */
.character-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: auto;
}

.details-btn {
  font-size: 11px;
  color: var(--nectar-purple);
  padding: 2px 8px;
  height: auto;
}

.details-btn:hover {
  color: var(--nectar-gold);
  background: rgba(114, 46, 209, 0.1);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .character-card {
    padding: 12px;
    min-height: 180px;
  }

  .character-name {
    font-size: 14px;
  }

  .character-avatar {
    width: 48px !important;
    height: 48px !important;
  }

  .attribute-item {
    gap: 6px;
  }

  .skills-list {
    gap: 2px;
  }
}
</style>
