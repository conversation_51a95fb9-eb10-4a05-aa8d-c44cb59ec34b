<template>
  <div class="workshop-container">
    <!-- 顶部导航 -->
    <div class="workshop-header">
      <a-button type="text" @click="goBack" class="back-btn">
        <template #icon>
          <ArrowLeftOutlined />
        </template>
        返回琉璃胭脂坊
      </a-button>

      <h1 class="workshop-title">{{ workshopInfo.name }}</h1>

      <div class="workshop-stats">
        <span class="available-slots">可用槽位: {{ availableSlots }}/{{ maxSlots }}</span>
      </div>
    </div>

    <!-- 工坊描述 -->
    <div class="workshop-description">
      <p>{{ workshopInfo.description }}</p>
      <div class="production-info" v-if="workshopInfo.type !== 'hall'">
        <span class="cost-info">消耗材料: {{ workshopInfo.materialCost }}</span>
        <span class="output-info">产出: {{ workshopInfo.output }}</span>
      </div>
    </div>

    <!-- 可派遣角色列表 -->
    <div class="available-characters">
      <h3>可派遣角色</h3>
      <div class="character-grid">
        <CharacterCard v-for="character in availableCharactersForDispatch" :key="character.id" :character="character"
          :selected="isCharacterSelected(character.id)" :selectable="true" :disabled="!canSelectCharacter(character)"
          @select="toggleCharacterSelection(character.id)" />
      </div>

      <div v-if="availableCharactersForDispatch.length === 0" class="no-characters">
        <a-empty description="暂无可派遣的角色" />
      </div>
    </div>

    <!-- 当前派遣的角色 -->
    <div class="current-dispatched" v-if="currentDispatchedCharacters.length > 0">
      <h3>当前派遣中的角色</h3>

      <!-- 工坊总产出预览 -->
      <div class="workshop-summary" v-if="workshopSummary">
        <a-card size="small" title="工坊总预计产出">
          <div class="summary-stats">
            <div class="stat-item">
              <span class="stat-label">派遣角色数:</span>
              <span class="stat-value">{{ workshopSummary.characterCount }}人</span>
            </div>
            <div class="stat-item" v-if="workshopSummary.totalExpectedOutput.money">
              <span class="stat-label">预计金币:</span>
              <span class="stat-value">+{{ workshopSummary.totalExpectedOutput.money }}💰</span>
            </div>
            <div class="stat-item" v-if="workshopSummary.totalExpectedOutput.materials">
              <span class="stat-label">预计材料:</span>
              <span class="stat-value">
                <span v-for="(amount, material) in workshopSummary.totalExpectedOutput.materials" :key="material">
                  {{ material }}+{{ amount }}
                </span>
              </span>
            </div>
            <div class="stat-item">
              <span class="stat-label">总体力消耗:</span>
              <span class="stat-value">{{ workshopSummary.totalCharacterCost.stamina }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">总心情消耗:</span>
              <span class="stat-value">{{ workshopSummary.totalCharacterCost.mood }}</span>
            </div>
          </div>
        </a-card>
      </div>

      <!-- 派遣角色详细列表 -->
      <div class="dispatched-grid">
        <div v-for="character in currentDispatchedCharacters" :key="character.id" class="dispatched-character">
          <a-avatar :src="`/src/assets/graphics/portraits/${character.portrait}`" :size="64"
            class="character-avatar clickable-avatar" @click="showCharacterDetails(character)">
            {{ character.name.charAt(0) }}
          </a-avatar>
          <div class="character-info">
            <h4>{{ character.name }}</h4>
            <a-tag :color="getTypeColor(character.type)" size="small">
              {{ getTypeText(character.type) }}
            </a-tag>
            <div class="character-activity">
              <span>{{ getActivityText(character.currentActivity) }}</span>
            </div>
            <!-- 显示该角色的预计产出 -->
            <div class="character-output" v-if="getCharacterDispatchInfo(character.id)">
              <div class="output-info">
                <span v-if="getCharacterDispatchInfo(character.id)?.expectedOutput?.money" class="output-item">
                  💰+{{ getCharacterDispatchInfo(character.id)?.expectedOutput?.money }}
                </span>
                <span v-if="getCharacterDispatchInfo(character.id)?.expectedOutput?.materials" class="output-item">
                  <span v-for="(amount, material) in getCharacterDispatchInfo(character.id)?.expectedOutput?.materials" :key="material">
                    {{ material }}+{{ amount }}
                  </span>
                </span>
              </div>
              <div class="cost-info">
                <span class="cost-item">体力-{{ getCharacterDispatchInfo(character.id)?.characterCost?.stamina || 0 }}</span>
                <span class="cost-item">心情-{{ getCharacterDispatchInfo(character.id)?.characterCost?.mood || 0 }}</span>
              </div>
            </div>
          </div>
          <a-button size="small" type="primary" danger @click="cancelDispatch(character.id)" class="cancel-btn">
            取消派遣
          </a-button>
        </div>
      </div>
    </div>

    <!-- 派遣槽位 -->
    <div class="dispatch-slots">
      <h3>派遣槽位</h3>
      <div class="slots-grid">
        <div v-for="(slot, index) in dispatchSlots" :key="index" class="dispatch-slot"
          :class="{ occupied: slot.characterId }">
          <div v-if="slot.characterId" class="assigned-character">
            <a-avatar :src="`/src/assets/graphics/portraits/${getCharacterById(slot.characterId)?.portrait}`" :size="48"
              class="character-avatar" />
            <span class="character-name">{{ getCharacterById(slot.characterId)?.name }}</span>
            <a-button size="small" type="text" @click="removeFromSlot(index)" class="remove-btn">
              <template #icon>
                <CloseOutlined />
              </template>
            </a-button>
          </div>
          <div v-else class="empty-slot">
            <PlusOutlined class="add-icon" />
            <span>点击角色卡片添加到此槽位</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 派遣预览和确认 -->
    <div class="dispatch-preview" v-if="hasSelectedCharacters">
      <h3>派遣预览</h3>
      <div class="preview-content">
        <div class="cost-preview">
          <h4>消耗预测</h4>
          <div class="cost-items">
            <span v-if="workshopInfo.type !== 'hall'">材料: {{ totalMaterialCost }}</span>
            <span>角色体力: {{ totalStaminaCost }}</span>
            <span>角色心情: {{ totalMoodCost }}</span>
          </div>
        </div>

        <div class="output-preview">
          <h4>产出预测</h4>
          <div class="output-items">
            <span v-if="workshopInfo.type === 'hall'">金币: +{{ predictedGoldOutput }}</span>
            <span v-else>{{ workshopInfo.output }}: +{{ predictedOutput }}</span>
            <span>角色经验: +{{ predictedExperience }}</span>
          </div>
        </div>
      </div>

      <div class="dispatch-actions">
        <a-button @click="clearSelection">清空选择</a-button>
        <a-button type="primary" @click="confirmDispatch" :loading="dispatching" :disabled="!canDispatch">
          确认派遣
        </a-button>
      </div>
    </div>

    <!-- 角色详情弹窗 -->
    <a-modal v-model:open="showDetailsModal" :title="`${selectedCharacterForDetails?.name} - 详细信息`" :footer="null"
      width="500px" centered>
      <CharacterDetails v-if="selectedCharacterForDetails" :character="selectedCharacterForDetails" />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import { ArrowLeftOutlined, PlusOutlined, CloseOutlined } from '@ant-design/icons-vue'
import { useGameStore } from '@/stores/gameStore'
import { useCharacterStore, type Character } from '@/stores/characterStore'
import CharacterCard from '@/components/CharacterCard.vue'
import CharacterDetails from '@/components/CharacterDetails.vue'

const router = useRouter()
const route = useRoute()
const gameStore = useGameStore()
const characterStore = useCharacterStore()

// 工坊类型
const workshopType = computed(() => route.params.type as string)

// 工坊信息配置
const workshopConfigs = {
  hall: {
    name: '会客大厅',
    description: '让少女们进行歌舞曲艺表演，获得金币收入',
    type: 'hall',
    materialCost: '无',
    output: '金币',
    maxSlots: 2
  },
  'holy-water': {
    name: '圣水工坊',
    description: '生产圣水，用于农场浇水和其他用途',
    type: 'production',
    materialCost: '种子 x2',
    output: '圣水',
    maxSlots: 2
  },
  honey: {
    name: '蜜酿工坊',
    description: '生产蜜酿，珍贵的材料和商品',
    type: 'production',
    materialCost: '灵果 x1',
    output: '蜜酿',
    maxSlots: 2
  },
  milk: {
    name: '乳液工坊',
    description: '生产乳液，高级材料和商品',
    type: 'production',
    materialCost: '圣水 x2, 蜜酿 x1',
    output: '乳液',
    maxSlots: 2
  },
  nectar: {
    name: '琼浆工坊',
    description: '生产琼浆，最珍贵的产品',
    type: 'production',
    materialCost: '乳液 x2, 蜜酿 x2',
    output: '琼浆',
    maxSlots: 2
  }
}

// 当前工坊信息
const workshopInfo = computed(() => {
  return workshopConfigs[workshopType.value as keyof typeof workshopConfigs] || workshopConfigs.hall
})

// 派遣相关状态
const selectedCharacters = ref<string[]>([])
const dispatchSlots = ref<Array<{ characterId: string | null }>>([])
const dispatching = ref(false)

// 角色详情弹窗状态
const showDetailsModal = ref(false)
const selectedCharacterForDetails = ref<Character | null>(null)

// 计算属性
const maxSlots = computed(() => workshopInfo.value.maxSlots)
const availableSlots = computed(() => {
  const currentlyDispatchedCount = currentDispatchedCharacters.value.length
  const pendingDispatchCount = dispatchSlots.value.filter(slot => slot.characterId).length
  return maxSlots.value - currentlyDispatchedCount - pendingDispatchCount
})

const hasSelectedCharacters = computed(() => {
  return dispatchSlots.value.some(slot => slot.characterId)
})

const canDispatch = computed(() => {
  return hasSelectedCharacters.value && !dispatching.value
})

// 可派遣角色（过滤状态）
const availableCharactersForDispatch = computed(() => {
  return characterStore.availableCharacters.filter(character =>
    character.status !== 'Exiled' &&
    character.status !== 'Overworked' &&
    !character.attributes.isAssigned
  )
})

// 检查角色是否被选中
const isCharacterSelected = (characterId: string): boolean => {
  return dispatchSlots.value.some(slot => slot.characterId === characterId)
}

// 检查角色是否可以选择
const canSelectCharacter = (character: Character): boolean => {
  // 检查角色状态
  if (character.status === 'Exiled' || character.status === 'Overworked') {
    return false
  }

  // 检查体力和心情
  if (character.stamina < 20 || character.mood < 20) {
    return false
  }

  return true
}

// 当前派遣的角色（按工坊类型过滤）
const currentDispatchedCharacters = computed(() => {
  return characterStore.characters.filter(character =>
    character.attributes.isAssigned &&
    character.currentActivity === 'Producing' &&
    character.attributes.assignedWorkshop === workshopType.value
  )
})

// 工坊派遣汇总信息
const workshopSummary = computed(() => {
  return gameStore.getWorkshopDispatchSummary(workshopType.value)
})

// 获取角色的派遣信息
const getCharacterDispatchInfo = (characterId: string) => {
  return gameStore.dispatch.getCharacterDispatch(characterId)
}

// 获取活动文本
const getActivityText = (activity: string): string => {
  const activityMap: Record<string, string> = {
    'Idle': '空闲',
    'Producing': '生产中',
    'Performing': '表演中',
    'Resting': '休息中'
  }
  return activityMap[activity] || activity
}

// 计算角色产出
const calculateCharacterOutput = (character: Character) => {
  const baseOutput = 10 // 基础产出
  const efficiency = character.level * 0.1 + 1 // 基础效率

  if (workshopType.value === 'hall') {
    return {
      money: Math.floor(baseOutput * efficiency)
    }
  } else {
    // 根据工坊类型确定产出材料
    const outputMaterial = workshopInfo.value.output || 'material'
    return {
      materials: {
        [outputMaterial]: Math.floor(baseOutput * efficiency)
      }
    }
  }
}

// 计算角色消耗
const calculateCharacterCost = (character: Character) => {
  const baseStamina = 15
  const baseMood = 10

  // 根据角色等级和类型调整消耗
  const staminaMultiplier = character.type === 'Special' ? 0.8 : character.type === 'Rare' ? 0.9 : 1.0
  const moodMultiplier = character.type === 'Special' ? 0.8 : character.type === 'Rare' ? 0.9 : 1.0

  return {
    stamina: Math.floor(baseStamina * staminaMultiplier),
    mood: Math.floor(baseMood * moodMultiplier)
  }
}

// 预测计算
const totalMaterialCost = computed(() => {
  const assignedCount = dispatchSlots.value.filter(slot => slot.characterId).length
  return assignedCount * 2 // 简化计算
})

const totalStaminaCost = computed(() => {
  return dispatchSlots.value.filter(slot => slot.characterId).length * 15
})

const totalMoodCost = computed(() => {
  return dispatchSlots.value.filter(slot => slot.characterId).length * 10
})

const predictedOutput = computed(() => {
  let total = 0
  dispatchSlots.value.forEach(slot => {
    if (slot.characterId) {
      const character = characterStore.getCharacterById(slot.characterId)
      if (character) {
        total += Math.floor(character.attributes.efficiency * 10)
      }
    }
  })
  return total
})

const predictedGoldOutput = computed(() => {
  return predictedOutput.value * 50 // 会客大厅金币产出
})

const predictedExperience = computed(() => {
  return dispatchSlots.value.filter(slot => slot.characterId).length * 20
})

// 方法
const goBack = () => {
  router.push('/llyzf')
}

const getTypeColor = (type: string) => {
  const colors = {
    Normal: 'default',
    Rare: 'blue',
    Special: 'purple'
  }
  return colors[type as keyof typeof colors] || 'default'
}

const getTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    'Normal': '普通',
    'Rare': '稀有',
    'Special': '特殊'
  }
  return typeMap[type] || type
}

const getCharacterById = (id: string): Character | undefined => {
  return characterStore.getCharacterById(id)
}

const toggleCharacterSelection = (characterId: string) => {
  // 找到第一个空槽位
  const emptySlotIndex = dispatchSlots.value.findIndex(slot => !slot.characterId)

  if (emptySlotIndex !== -1) {
    // 检查角色是否已经在其他槽位
    const existingSlotIndex = dispatchSlots.value.findIndex(slot => slot.characterId === characterId)

    if (existingSlotIndex !== -1) {
      // 如果已存在，则移除
      dispatchSlots.value[existingSlotIndex].characterId = null
    } else {
      // 添加到空槽位
      dispatchSlots.value[emptySlotIndex].characterId = characterId
    }
  } else {
    message.warning('所有槽位已满')
  }
}

const removeFromSlot = (slotIndex: number) => {
  dispatchSlots.value[slotIndex].characterId = null
}

const clearSelection = () => {
  dispatchSlots.value.forEach(slot => {
    slot.characterId = null
  })
  selectedCharacters.value = []
}

const cancelDispatch = (characterId: string) => {
  // 从派遣队列中移除
  const dispatchRemoved = gameStore.removeFromDispatchQueue(characterId)
  // 取消角色分配
  const characterUnassigned = characterStore.unassignCharacter(characterId)

  if (dispatchRemoved && characterUnassigned) {
    message.success('取消派遣成功')
  } else {
    message.error('取消派遣失败')
  }
}

const showCharacterDetails = (character: Character) => {
  selectedCharacterForDetails.value = character
  showDetailsModal.value = true
}

const confirmDispatch = async () => {
  dispatching.value = true

  try {
    // 模拟派遣过程
    await new Promise(resolve => setTimeout(resolve, 1500))

    // 处理派遣逻辑
    const assignedCharacters: string[] = []

    dispatchSlots.value.forEach(slot => {
      if (slot.characterId) {
        const character = characterStore.getCharacterById(slot.characterId)
        if (character) {
          // 计算预期产出和消耗
          const expectedOutput = calculateCharacterOutput(character)
          const characterCost = calculateCharacterCost(character)

          // 使用新的派遣状态管理
          const success = gameStore.addToDispatchQueue(
            slot.characterId,
            workshopType.value,
            'Producing',
            expectedOutput,
            characterCost
          )

          if (success) {
            // 分配角色到工坊
            characterStore.assignCharacter(slot.characterId, 'Producing', workshopType.value, gameStore.currentDay)
            // 消耗角色状态
            characterStore.consumeCharacterStats(slot.characterId, characterCost.stamina, characterCost.mood)
            assignedCharacters.push(slot.characterId)
          }
        }
      }
    })

    if (assignedCharacters.length > 0) {
      // 消耗材料（如果需要）
      if (workshopInfo.value.type !== 'hall') {
        // 这里应该调用gameStore的消耗材料方法
        // gameStore.consumeMaterial(...)
      }

      // 将产出添加到产出队列而不是立即结算
      if (workshopInfo.value.type === 'hall') {
        gameStore.addToProductionQueue({
          type: 'money',
          amount: predictedGoldOutput.value,
          source: `${workshopInfo.value.name}派遣`
        })
      } else {
        // 其他工坊的材料产出也添加到队列
        // gameStore.addToProductionQueue({
        //   type: 'material',
        //   materialType: 'xxx',
        //   amount: xxx,
        //   source: `${workshopInfo.value.name}派遣`
        // })
      }

      // 将经验添加到产出队列
      assignedCharacters.forEach(characterId => {
        gameStore.addToProductionQueue({
          type: 'experience',
          characterId,
          amount: 20,
          source: `${workshopInfo.value.name}派遣`
        })
      })

      message.success(`成功派遣${assignedCharacters.length}名角色`)

      // 保存数据
      characterStore.saveCharacters()
      gameStore.saveGame()

      // 返回LLYZF界面
      router.push('/llyzf')
    } else {
      message.error('派遣失败')
    }
  } catch (error) {
    message.error('派遣过程中发生错误')
  } finally {
    dispatching.value = false
  }
}

// 初始化
onMounted(() => {
  // 检查工坊是否存在
  if (!workshopConfigs[workshopType.value as keyof typeof workshopConfigs]) {
    message.error('未知的工坊类型')
    router.push('/llyzf')
    return
  }

  // 检查时间段
  if (gameStore.currentTimeSlot !== '上午') {
    message.warning('只能在上午时段进行派遣')
    router.push('/llyzf')
    return
  }

  // 初始化槽位（根据可用槽位数量）
  const availableSlotCount = Math.max(0, maxSlots.value - currentDispatchedCharacters.value.length)
  dispatchSlots.value = Array(availableSlotCount).fill(null).map(() => ({ characterId: null }))

  message.success(`进入${workshopInfo.value.name}`)
})
</script>

<style scoped>
.workshop-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.workshop-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  background: white;
  padding: 16px 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.workshop-title {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  color: var(--nectar-purple);
}

.available-slots {
  font-size: 14px;
  color: #666;
}

.workshop-description {
  background: white;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.production-info {
  margin-top: 12px;
  display: flex;
  gap: 20px;
}

.cost-info {
  color: #e74c3c;
  font-size: 14px;
}

.output-info {
  color: #27ae60;
  font-size: 14px;
}

.available-characters {
  background: white;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.character-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-top: 16px;
}

.current-dispatched {
  background: white;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dispatched-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.dispatched-character {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f9f9f9;
  border-radius: 8px;
  border-left: 4px solid #52c41a;
}

.dispatched-character .character-info {
  flex: 1;
  text-align: left;
}

.dispatched-character .character-info h4 {
  margin: 0 0 4px 0;
  color: #333;
}

.dispatched-character .character-activity {
  margin-top: 8px;
  font-size: 12px;
  color: #666;
}

.cancel-btn {
  flex-shrink: 0;
}

.stat {
  font-size: 12px;
  color: #666;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 4px;
}

.character-skills {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  margin-top: 8px;
}

.dispatch-slots {
  background: white;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.slots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.dispatch-slot {
  border: 2px dashed var(--nectar-gold);
  border-radius: 12px;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  background: rgba(212, 175, 55, 0.1);
}

.dispatch-slot.occupied {
  border-style: solid;
  background: white;
}

.assigned-character {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  position: relative;
}

.remove-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #ff4d4f;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #999;
  text-align: center;
}

.add-icon {
  font-size: 24px;
}

.dispatch-preview {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.preview-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin: 16px 0;
}

.cost-preview,
.output-preview {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.cost-items,
.output-items {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-top: 8px;
}

.cost-items span {
  color: #e74c3c;
  font-size: 14px;
}

.output-items span {
  color: #27ae60;
  font-size: 14px;
}

.dispatch-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 16px;
}

.no-characters {
  text-align: center;
  padding: 40px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .workshop-container {
    padding: 16px;
  }

  .workshop-header {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .character-grid {
    grid-template-columns: 1fr;
  }

  .slots-grid {
    grid-template-columns: 1fr;
  }

  .preview-content {
    grid-template-columns: 1fr;
  }

  .dispatch-actions {
    flex-direction: column;
  }
}

.clickable-avatar {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.clickable-avatar:hover {
  transform: scale(1.05);
}

.workshop-summary {
  margin-bottom: 16px;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f5f5f5;
  border-radius: 6px;
}

.stat-label {
  font-weight: 500;
  color: #666;
}

.stat-value {
  font-weight: 600;
  color: #1890ff;
}

.character-output {
  margin-top: 8px;
  font-size: 12px;
}

.output-info {
  color: #52c41a;
  margin-bottom: 4px;
}

.cost-info {
  color: #ff4d4f;
}

.output-item,
.cost-item {
  margin-right: 8px;
}
</style>
