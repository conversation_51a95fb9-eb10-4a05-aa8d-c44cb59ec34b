<template>
  <div class="character-list-container">
    <!-- 顶部导航 -->
    <div class="character-list-header">
      <a-button type="text" @click="goBack" class="back-btn">
        <template #icon>
          <ArrowLeftOutlined />
        </template>
        返回地图
      </a-button>

      <h1 class="page-title">角色列表</h1>

      <div class="character-count">
        <a-tag color="purple">
          共{{ filteredCharacters.length }}名角色
        </a-tag>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="filter-section">
      <div class="search-bar">
        <a-input-search
          v-model:value="searchQuery"
          placeholder="搜索角色姓名..."
          allow-clear
          @search="onSearch"
          style="width: 300px"
        />
      </div>

      <div class="filter-controls">
        <a-select
          v-model:value="selectedType"
          placeholder="角色类型"
          style="width: 120px"
          allow-clear
        >
          <a-select-option value="">全部类型</a-select-option>
          <a-select-option value="Normal">普通</a-select-option>
          <a-select-option value="Rare">稀有</a-select-option>
          <a-select-option value="Special">特殊</a-select-option>
        </a-select>

        <a-select
          v-model:value="selectedStatus"
          placeholder="角色状态"
          style="width: 120px"
          allow-clear
        >
          <a-select-option value="">全部状态</a-select-option>
          <a-select-option value="Normal">正常</a-select-option>
          <a-select-option value="Tired">疲惫</a-select-option>
          <a-select-option value="Overworked">过劳</a-select-option>
          <a-select-option value="Resting">休息</a-select-option>
          <a-select-option value="Exiled">流放</a-select-option>
        </a-select>

        <a-select
          v-model:value="sortBy"
          placeholder="排序方式"
          style="width: 150px"
        >
          <a-select-option value="meetDate">邂逅时间</a-select-option>
          <a-select-option value="level">等级</a-select-option>
          <a-select-option value="name">姓名</a-select-option>
          <a-select-option value="type">类型</a-select-option>
        </a-select>

        <a-select
          v-model:value="sortOrder"
          style="width: 100px"
        >
          <a-select-option value="desc">降序</a-select-option>
          <a-select-option value="asc">升序</a-select-option>
        </a-select>
      </div>
    </div>

    <!-- 角色网格 -->
    <div class="character-grid">
      <CharacterCard
        v-for="character in paginatedCharacters"
        :key="character.id"
        :character="character"
        :selectable="false"
        @click="showCharacterDetails(character)"
      />
    </div>

    <!-- 空状态 -->
    <div v-if="filteredCharacters.length === 0" class="empty-state">
      <a-empty description="没有找到符合条件的角色" />
    </div>

    <!-- 分页 -->
    <div class="pagination-section" v-if="filteredCharacters.length > pageSize">
      <a-pagination
        v-model:current="currentPage"
        :total="filteredCharacters.length"
        :page-size="pageSize"
        :show-size-changer="false"
        :show-quick-jumper="true"
        :show-total="(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`"
        @change="onPageChange"
      />
    </div>

    <!-- 角色详情弹窗 -->
    <a-modal
      v-model:open="showDetailsModal"
      :title="`${selectedCharacterForDetails?.name} - 详细信息`"
      :footer="null"
      width="500px"
      centered
    >
      <CharacterDetails v-if="selectedCharacterForDetails" :character="selectedCharacterForDetails" />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { ArrowLeftOutlined } from '@ant-design/icons-vue'
import { useCharacterStore, type Character } from '@/stores/characterStore'
import CharacterCard from '@/components/CharacterCard.vue'
import CharacterDetails from '@/components/CharacterDetails.vue'

const router = useRouter()
const characterStore = useCharacterStore()

// 搜索和筛选状态
const searchQuery = ref('')
const selectedType = ref('')
const selectedStatus = ref('')
const sortBy = ref('meetDate')
const sortOrder = ref('desc')

// 分页状态
const currentPage = ref(1)
const pageSize = ref(12)

// 角色详情弹窗状态
const showDetailsModal = ref(false)
const selectedCharacterForDetails = ref<Character | null>(null)

// 计算属性：筛选后的角色列表
const filteredCharacters = computed(() => {
  let characters = [...characterStore.characters]

  // 搜索过滤
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.trim().toLowerCase()
    characters = characters.filter(char =>
      char.name.toLowerCase().includes(query)
    )
  }

  // 类型过滤
  if (selectedType.value) {
    characters = characters.filter(char => char.type === selectedType.value)
  }

  // 状态过滤
  if (selectedStatus.value) {
    characters = characters.filter(char => char.status === selectedStatus.value)
  }

  // 排序
  characters.sort((a, b) => {
    let aValue: any, bValue: any

    switch (sortBy.value) {
      case 'meetDate':
        aValue = new Date(a.meetDate).getTime()
        bValue = new Date(b.meetDate).getTime()
        break
      case 'level':
        aValue = a.level
        bValue = b.level
        break
      case 'name':
        aValue = a.name
        bValue = b.name
        break
      case 'type':
        aValue = a.type
        bValue = b.type
        break
      default:
        aValue = new Date(a.meetDate).getTime()
        bValue = new Date(b.meetDate).getTime()
    }

    if (sortOrder.value === 'asc') {
      return aValue > bValue ? 1 : -1
    } else {
      return aValue < bValue ? 1 : -1
    }
  })

  return characters
})

// 计算属性：分页后的角色列表
const paginatedCharacters = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredCharacters.value.slice(start, end)
})

// 方法
const goBack = () => {
  router.push('/main')
}

const onSearch = () => {
  currentPage.value = 1 // 搜索时重置到第一页
}

const onPageChange = (page: number) => {
  currentPage.value = page
}

const showCharacterDetails = (character: Character) => {
  selectedCharacterForDetails.value = character
  showDetailsModal.value = true
}

onMounted(() => {
  // 加载角色数据
  try {
    if (!characterStore.loadCharacters()) {
      message.warning('暂无角色数据')
    }
  } catch (error) {
    console.error('Failed to load characters:', error)
    message.error('角色数据加载失败')
  }
})
</script>

<style scoped>
.character-list-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.character-list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  background: white;
  padding: 16px 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.page-title {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  background: linear-gradient(135deg, var(--nectar-purple), var(--nectar-gold));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.filter-section {
  background: white;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.search-bar {
  margin-bottom: 16px;
}

.filter-controls {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.character-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.empty-state {
  background: white;
  padding: 40px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.pagination-section {
  display: flex;
  justify-content: center;
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .character-list-container {
    padding: 16px;
  }

  .character-list-header {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .search-bar {
    width: 100%;
  }

  .search-bar .ant-input-search {
    width: 100% !important;
  }

  .filter-controls {
    justify-content: center;
  }

  .character-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
</style>
