<template>
  <div class="bedroom-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <a-button type="text" @click="router.back()" class="back-button">
        <template #icon>
          <ArrowLeftOutlined />
        </template>
        返回
      </a-button>
      <h1>🛏️ 寝室</h1>
      <p class="subtitle">夜晚时光，恢复体力</p>
    </div>

    <!-- 寝室活动选择 -->
    <div v-if="!showSettlement" class="bedroom-activities">
      <h2>选择夜晚活动</h2>

      <!-- 基础活动 -->
      <div class="basic-activities">
        <div class="activity-card" :class="{ disabled: hasEaten }" @click="eatMeal">
          <div class="activity-icon">🍽️</div>
          <div class="activity-content">
            <h3>用餐</h3>
            <p>恢复少量体力 (+10)</p>
            <p class="activity-limit">{{ hasEaten ? '今日已用餐' : '每日限一次' }}</p>
          </div>
        </div>

        <div class="activity-card" :class="{ disabled: hasBathed }" @click="takeBath">
          <div class="activity-icon">🛁</div>
          <div class="activity-content">
            <h3>洗澡</h3>
            <p>恢复少量体力 (+10)</p>
            <p class="activity-limit">{{ hasBathed ? '今日已洗澡' : '每日限一次' }}</p>
          </div>
        </div>

        <div class="activity-card" @click="goToSleep">
          <div class="activity-icon">😴</div>
          <div class="activity-content">
            <h3>睡觉</h3>
            <p>直接进入单日结算</p>
            <p class="activity-limit">结束今日</p>
          </div>
        </div>
      </div>

      <!-- 特殊活动（条件解锁） -->
      <div v-if="canDoubleRest || canGroupRest" class="special-activities">
        <h3>特殊修炼</h3>

        <div v-if="canDoubleRest" class="activity-card special" @click="startDoubleRest">
          <div class="activity-icon">💕</div>
          <div class="activity-content">
            <h3>双修</h3>
            <p>与一名角色进行双修</p>
            <p class="activity-cost">消耗体力: 20-30</p>
          </div>
        </div>

        <div v-if="canGroupRest" class="activity-card special" @click="startGroupRest">
          <div class="activity-icon">👥</div>
          <div class="activity-content">
            <h3>群修</h3>
            <p>与多名角色进行群修</p>
            <p class="activity-cost">消耗体力: 30-50</p>
          </div>
        </div>
      </div>

      <!-- 产出队列展示 -->
      <div v-if="gameStore.pendingQueue.length > 0" class="production-queue-section">
        <h2>待结算收益</h2>
        <div class="queue-summary">
          <div class="summary-item">
            <div class="summary-icon">💰</div>
            <div class="summary-content">
              <span class="summary-label">金币</span>
              <span class="summary-value">{{ pendingMoney }}</span>
            </div>
          </div>
          <div class="summary-item">
            <div class="summary-icon">📦</div>
            <div class="summary-content">
              <span class="summary-label">材料</span>
              <span class="summary-value">{{ pendingMaterialsCount }}种</span>
            </div>
          </div>
          <div class="summary-item">
            <div class="summary-icon">✨</div>
            <div class="summary-content">
              <span class="summary-label">经验</span>
              <span class="summary-value">{{ pendingExperienceCount }}人</span>
            </div>
          </div>
        </div>

        <!-- 详细产出列表 -->
        <div class="production-details">
          <a-collapse ghost>
            <a-collapse-panel key="1" header="查看详细产出">
              <div class="production-items">
                <div v-for="item in gameStore.pendingQueue" :key="item.id" class="production-item-mini">
                  <div class="item-info">
                    <span class="item-icon">{{ getProductionIcon(item) }}</span>
                    <span class="item-name">{{ getProductionName(item) }}</span>
                    <span class="item-amount">×{{ item.amount }}</span>
                  </div>
                  <div v-if="item.characterInfo" class="item-character">
                    <a-avatar :src="getCharacterPortraitUrl(item.characterInfo.portrait)" :size="16">
                      {{ item.characterInfo.name.charAt(0) }}
                    </a-avatar>
                    <span class="character-name">{{ item.characterInfo.name }}</span>
                  </div>
                </div>
              </div>
            </a-collapse-panel>
          </a-collapse>
        </div>
      </div>
    </div>

    <!-- 角色选择模态框 -->
    <a-modal v-model:open="showCharacterSelection" :title="selectionTitle" width="800px" :footer="null">
      <div class="character-selection">
        <div class="selection-info">
          <p v-if="selectionMode === 'double'">选择一名角色进行双修</p>
          <p v-else-if="selectionMode === 'group'">选择最多5名角色进行群修</p>
        </div>

        <div class="character-grid">
          <div v-for="character in availableCharactersForRest" :key="character.id" class="character-selection-card"
            :class="{ selected: selectedCharacters.includes(character.id) }"
            @click="toggleCharacterSelection(character.id)">
            <div class="character-avatar">
              <img :src="character.portrait" :alt="character.name" />
            </div>
            <div class="character-info">
              <h4>{{ character.name }}</h4>
              <div class="character-stats">
                <div class="stat-item">
                  <span class="stat-label">体力</span>
                  <a-progress :percent="character.stamina" size="small"
                    :stroke-color="getStaminaColor(character.stamina)" />
                </div>
                <div class="stat-item">
                  <span class="stat-label">心情</span>
                  <a-progress :percent="character.mood" size="small" :stroke-color="getMoodColor(character.mood)" />
                </div>
              </div>
              <div class="character-type">
                <a-tag :color="getTypeColor(character.type)">{{ getTypeText(character.type) }}</a-tag>
              </div>
            </div>
          </div>
        </div>

        <div class="selection-actions">
          <a-button @click="cancelSelection">取消</a-button>
          <a-button type="primary" @click="confirmSelection" :disabled="selectedCharacters.length === 0">
            确认选择 ({{ selectedCharacters.length }})
          </a-button>
        </div>
      </div>
    </a-modal>

    <!-- 单日结算界面 -->
    <div v-if="showSettlement" class="settlement-screen">
      <div class="settlement-header">
        <h2>🌙 单日结算</h2>
        <p>第{{ gameStore.currentDay }}天结算报告</p>
      </div>

      <div class="settlement-content">
        <!-- 收益结算 -->
        <div class="settlement-section">
          <h3>💰 今日收益</h3>
          <div class="settlement-item">
            <span class="item-label">灵石变化:</span>
            <span class="item-value" :class="{ positive: dailyGemChange >= 0, negative: dailyGemChange < 0 }">
              {{ dailyGemChange >= 0 ? '+' : '' }}{{ dailyGemChange }}
            </span>
          </div>
          <div class="settlement-item">
            <span class="item-label">金币变化:</span>
            <span class="item-value" :class="{ positive: dailyMoneyChange >= 0, negative: dailyMoneyChange < 0 }">
              {{ dailyMoneyChange >= 0 ? '+' : '' }}{{ dailyMoneyChange }}
            </span>
          </div>
        </div>

        <!-- 角色状态变化 -->
        <div class="settlement-section">
          <h3>👥 角色状态变化</h3>
          <div class="character-changes">
            <div v-for="change in characterChanges" :key="change.id" class="character-change">
              <div class="character-avatar">
                <img :src="change.portrait" :alt="change.name" />
              </div>
              <div class="change-info">
                <h4>{{ change.name }}</h4>
                <div class="stat-changes">
                  <div v-if="change.staminaChange !== 0" class="stat-change">
                    <span>体力: </span>
                    <span :class="{ positive: change.staminaChange > 0, negative: change.staminaChange < 0 }">
                      {{ change.staminaChange > 0 ? '+' : '' }}{{ change.staminaChange }}
                    </span>
                  </div>
                  <div v-if="change.moodChange !== 0" class="stat-change">
                    <span>心情: </span>
                    <span :class="{ positive: change.moodChange > 0, negative: change.moodChange < 0 }">
                      {{ change.moodChange > 0 ? '+' : '' }}{{ change.moodChange }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 农场状态 -->
        <div class="settlement-section">
          <h3>🌱 农场状况</h3>
          <div class="farm-status">
            <p>作物生长良好，预计明日可收获</p>
          </div>
        </div>
      </div>

      <div class="settlement-actions">
        <a-button type="primary" size="large" @click="finishSettlement" :loading="settling">
          完成结算，进入新的一天
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { ArrowLeftOutlined } from '@ant-design/icons-vue'
import { useGameStore } from '@/stores/gameStore'
import { useCharacterStore } from '@/stores/characterStore'

const router = useRouter()
const gameStore = useGameStore()
const characterStore = useCharacterStore()

// 状态管理
const showSettlement = ref(false)
const showCharacterSelection = ref(false)
const selectionMode = ref<'double' | 'group'>('double')
const selectedCharacters = ref<string[]>([])
const settling = ref(false)

// 每日活动状态
const hasEaten = ref(false)
const hasBathed = ref(false)

// 结算数据
const dailyGemChange = ref(0)
const dailyMoneyChange = ref(0)
const characterChanges = ref<Array<{
  id: string
  name: string
  portrait: string
  staminaChange: number
  moodChange: number
}>>([])

// 计算属性
const selectionTitle = computed(() => {
  return selectionMode.value === 'double' ? '选择双修角色' : '选择群修角色'
})

const canDoubleRest = computed(() => {
  // TODO: 检查是否完成单个角色支线
  return true // 暂时返回true用于测试
})

const canGroupRest = computed(() => {
  // TODO: 检查是否完成所有角色支线
  return true // 暂时返回true用于测试
})

const availableCharactersForRest = computed(() => {
  return characterStore.characters.filter(character =>
    character.status !== 'Exiled' && !character.attributes.isAssigned
  )
})

const pendingMoney = computed(() => {
  return gameStore.pendingMoney
})

const pendingMaterialsCount = computed(() => {
  return gameStore.pendingMaterialsCount
})

const pendingExperienceCount = computed(() => {
  return gameStore.pendingExperienceCount
})

// 初始化
onMounted(() => {
  // 重置每日活动状态
  hasEaten.value = false
  hasBathed.value = false

  // 自动结算产出队列
  if (gameStore.pendingQueue.length > 0) {
    settleProductionQueue()
  }
})

// 基础活动函数
const eatMeal = () => {
  if (hasEaten.value) {
    message.warning('今日已经用餐过了')
    return
  }

  hasEaten.value = true
  gameStore.restoreEnergy(10)
  message.success('用餐完毕，体力恢复 +10')
}

const takeBath = () => {
  if (hasBathed.value) {
    message.warning('今日已经洗澡过了')
    return
  }

  hasBathed.value = true
  gameStore.restoreEnergy(10)
  message.success('洗澡完毕，体力恢复 +10')
}

const goToSleep = () => {
  startSettlement()
}

// 特殊修炼函数
const startDoubleRest = () => {
  if (gameStore.playerResources.energy < 20) {
    message.error('体力不足，无法进行双修')
    return
  }

  selectionMode.value = 'double'
  selectedCharacters.value = []
  showCharacterSelection.value = true
}

const startGroupRest = () => {
  if (gameStore.playerResources.energy < 30) {
    message.error('体力不足，无法进行群修')
    return
  }

  selectionMode.value = 'group'
  selectedCharacters.value = []
  showCharacterSelection.value = true
}

// 角色选择函数
const toggleCharacterSelection = (characterId: string) => {
  const index = selectedCharacters.value.indexOf(characterId)

  if (index > -1) {
    selectedCharacters.value.splice(index, 1)
  } else {
    if (selectionMode.value === 'double' && selectedCharacters.value.length >= 1) {
      selectedCharacters.value = [characterId]
    } else if (selectionMode.value === 'group' && selectedCharacters.value.length >= 5) {
      message.warning('最多只能选择5名角色')
      return
    } else {
      selectedCharacters.value.push(characterId)
    }
  }
}

const cancelSelection = () => {
  showCharacterSelection.value = false
  selectedCharacters.value = []
}

const confirmSelection = () => {
  if (selectedCharacters.value.length === 0) {
    message.warning('请选择至少一名角色')
    return
  }

  showCharacterSelection.value = false

  if (selectionMode.value === 'double') {
    performDoubleRest()
  } else {
    performGroupRest()
  }
}

// 执行修炼
const performDoubleRest = () => {
  const energyCost = Math.floor(Math.random() * 11) + 20 // 20-30
  gameStore.consumeEnergy(energyCost)

  // 恢复选中角色的体力和心情
  selectedCharacters.value.forEach(characterId => {
    const character = characterStore.getCharacterById(characterId)
    if (character) {
      const staminaRecover = Math.floor(Math.random() * 21) + 30 // 30-50
      const moodRecover = Math.floor(Math.random() * 21) + 20 // 20-40

      character.stamina = Math.min(100, character.stamina + staminaRecover)
      character.mood = Math.min(100, character.mood + moodRecover)
    }
  })

  message.success(`双修完成，消耗体力 ${energyCost}`)
  startSettlement()
}

const performGroupRest = () => {
  const energyCost = Math.floor(Math.random() * 21) + 30 // 30-50
  gameStore.consumeEnergy(energyCost)

  // 恢复选中角色的体力和心情
  selectedCharacters.value.forEach(characterId => {
    const character = characterStore.getCharacterById(characterId)
    if (character) {
      const staminaRecover = Math.floor(Math.random() * 31) + 40 // 40-70
      const moodRecover = Math.floor(Math.random() * 31) + 30 // 30-60

      character.stamina = Math.min(100, character.stamina + staminaRecover)
      character.mood = Math.min(100, character.mood + moodRecover)
    }
  })

  message.success(`群修完成，消耗体力 ${energyCost}`)
  startSettlement()
}

// 结算相关函数
const settleProductionQueue = () => {
  const result = gameStore.processProductionQueue()

  // 处理经验奖励
  Object.entries(result.experience).forEach(([characterId, exp]) => {
    characterStore.addExperience(characterId, exp as number)
  })

  dailyMoneyChange.value += result.money
}

const startSettlement = () => {
  // 记录角色状态变化
  characterChanges.value = characterStore.characters.map(character => ({
    id: character.id,
    name: character.name,
    portrait: character.portrait,
    staminaChange: 0, // 这里可以记录实际的变化值
    moodChange: 0
  }))

  showSettlement.value = true
}

const finishSettlement = async () => {
  settling.value = true

  try {
    // 模拟结算过程
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 推进到新的一天
    gameStore.advanceTime()

    // 触发角色每日恢复
    characterStore.dailyRecovery(gameStore.currentDay)

    // 保存游戏数据
    gameStore.saveGame()
    characterStore.saveCharacters()

    message.success('结算完成，新的一天开始了！')

    // 返回主场景
    router.push('/main')

  } catch (error) {
    message.error('结算过程中发生错误')
  } finally {
    settling.value = false
  }
}

// 工具函数
const getStaminaColor = (stamina: number): string => {
  if (stamina >= 70) return '#52c41a'
  if (stamina >= 40) return '#faad14'
  return '#ff4d4f'
}

const getMoodColor = (mood: number): string => {
  if (mood >= 70) return '#722ed1'
  if (mood >= 40) return '#faad14'
  return '#ff4d4f'
}

const getTypeColor = (type: string): string => {
  const colorMap: Record<string, string> = {
    'Normal': 'default',
    'Rare': 'blue',
    'Special': 'purple'
  }
  return colorMap[type] || 'default'
}

const getTypeText = (type: string): string => {
  const textMap: Record<string, string> = {
    'Normal': '普通',
    'Rare': '稀有',
    'Special': '特殊'
  }
  return textMap[type] || type
}

// 产出队列相关方法
const getProductionIcon = (item: any) => {
  switch (item.type) {
    case 'money': return '💰'
    case 'material': return getMaterialIcon(item.materialType)
    case 'experience': return '✨'
    default: return '❓'
  }
}

const getMaterialIcon = (materialType: string) => {
  const icons: Record<string, string> = {
    seeds: '🌱',
    fruits: '🍎',
    water: '💧',
    honey: '🍯',
    milk: '🥛',
    nectar: '🧪'
  }
  return icons[materialType] || '📦'
}

const getProductionName = (item: any) => {
  switch (item.type) {
    case 'money': return '金币'
    case 'material': return getMaterialName(item.materialType)
    case 'experience': return '经验值'
    default: return '未知物品'
  }
}

const getMaterialName = (materialType: string) => {
  const names: Record<string, string> = {
    seeds: '种子',
    fruits: '灵果',
    water: '圣水',
    honey: '蜜酿',
    milk: '乳液',
    nectar: '琼浆'
  }
  return names[materialType] || materialType
}

const getCharacterPortraitUrl = (portrait: string) => {
  // 如果是完整URL，直接返回
  if (portrait.startsWith('http')) {
    return portrait
  }
  // 否则构建本地路径
  return `/src/assets/graphics/portraits/${portrait}`
}
</script>

<style scoped>
.bedroom-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  position: relative;
  color: white;
}

.back-button {
  position: absolute;
  left: 0;
  top: 0;
  color: white;
}

.page-header h1 {
  margin: 0;
  font-size: 28px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
  margin: 8px 0 0 0;
  opacity: 0.9;
}

.bedroom-activities {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 20px;
  backdrop-filter: blur(10px);
}

.bedroom-activities h2 {
  margin: 0 0 20px 0;
  color: #333;
  text-align: center;
}

.basic-activities {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  margin-bottom: 30px;
}

.special-activities {
  border-top: 1px solid #f0f0f0;
  padding-top: 20px;
}

.special-activities h3 {
  margin: 0 0 16px 0;
  color: #722ed1;
  text-align: center;
}

.activity-card {
  display: flex;
  align-items: center;
  background: white;
  padding: 20px;
  border-radius: 12px;
  border: 2px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.activity-card:hover:not(.disabled) {
  border-color: #722ed1;
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(114, 46, 209, 0.2);
}

.activity-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #f5f5f5;
}

.activity-card.special {
  border-color: #722ed1;
  background: linear-gradient(135deg, #f6f3ff 0%, #ede7ff 100%);
}

.activity-card.special:hover {
  border-color: #531dab;
  background: linear-gradient(135deg, #ede7ff 0%, #d6ccff 100%);
}

.activity-icon {
  font-size: 32px;
  margin-right: 16px;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-content h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 18px;
}

.activity-content p {
  margin: 4px 0;
  color: #666;
  font-size: 14px;
}

.activity-limit {
  font-size: 12px !important;
  color: #999 !important;
  font-style: italic;
}

.activity-cost {
  font-size: 12px !important;
  color: #722ed1 !important;
  font-weight: 500;
}

.production-queue-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 20px;
  margin-top: 20px;
  backdrop-filter: blur(10px);
}

.production-queue-section h2 {
  margin: 0 0 16px 0;
  color: #333;
}

.queue-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
}

.summary-item {
  display: flex;
  align-items: center;
  background: white;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.summary-icon {
  font-size: 20px;
  margin-right: 8px;
}

.summary-content {
  display: flex;
  flex-direction: column;
}

.summary-label {
  font-size: 11px;
  color: #666;
  margin-bottom: 2px;
}

.summary-value {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.production-details {
  margin-top: 16px;
}

.production-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.production-item-mini {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #fafafa;
  border-radius: 6px;
  border-left: 3px solid #1890ff;
}

.item-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.item-icon {
  font-size: 16px;
}

.item-name {
  font-weight: 500;
  color: #262626;
}

.item-amount {
  color: #1890ff;
  font-weight: 600;
}

.item-character {
  display: flex;
  align-items: center;
  gap: 6px;
}

.character-name {
  font-size: 12px;
  color: #8c8c8c;
}

.character-selection {
  padding: 16px 0;
}

.selection-info {
  text-align: center;
  margin-bottom: 20px;
}

.selection-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.character-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 20px;
}

.character-selection-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #fafafa;
  padding: 16px;
  border-radius: 8px;
  border: 2px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.character-selection-card:hover {
  border-color: #722ed1;
  background: #f6f3ff;
}

.character-selection-card.selected {
  border-color: #722ed1;
  background: #ede7ff;
  box-shadow: 0 0 0 2px rgba(114, 46, 209, 0.2);
}

.character-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 12px;
}

.character-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.character-info {
  text-align: center;
  width: 100%;
}

.character-info h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 14px;
}

.character-stats {
  margin-bottom: 8px;
}

.stat-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 11px;
  color: #666;
  width: 30px;
}

.character-type {
  margin-top: 4px;
}

.selection-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
}

.settlement-screen {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(10px);
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.settlement-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #f0f0f0;
}

.settlement-header h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 24px;
}

.settlement-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.settlement-content {
  margin-bottom: 30px;
}

.settlement-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.settlement-section h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 18px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.settlement-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f5f5f5;
}

.settlement-item:last-child {
  border-bottom: none;
}

.item-label {
  color: #666;
  font-size: 14px;
}

.item-value {
  font-weight: bold;
  font-size: 16px;
}

.item-value.positive {
  color: #52c41a;
}

.item-value.negative {
  color: #ff4d4f;
}

.character-changes {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 12px;
}

.character-change {
  display: flex;
  align-items: center;
  background: #fafafa;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.character-change .character-avatar {
  width: 40px;
  height: 40px;
  margin-right: 12px;
  margin-bottom: 0;
}

.change-info {
  flex: 1;
}

.change-info h4 {
  margin: 0 0 4px 0;
  color: #333;
  font-size: 14px;
}

.stat-changes {
  display: flex;
  gap: 12px;
}

.stat-change {
  font-size: 12px;
}

.stat-change .positive {
  color: #52c41a;
}

.stat-change .negative {
  color: #ff4d4f;
}

.farm-status {
  text-align: center;
  color: #666;
  font-style: italic;
}

.settlement-actions {
  text-align: center;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .bedroom-container {
    padding: 16px;
  }

  .basic-activities {
    grid-template-columns: 1fr;
  }

  .character-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }

  .character-changes {
    grid-template-columns: 1fr;
  }

  .activity-card {
    flex-direction: column;
    text-align: center;
  }

  .activity-icon {
    margin: 0 0 12px 0;
  }
}
</style>
