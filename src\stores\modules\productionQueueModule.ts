import { ref, computed } from 'vue'

// 产出队列项目类型
export interface ProductionQueueItem {
  id: string                                    // 唯一标识
  type: 'material' | 'money' | 'experience'    // 产出类型
  characterId?: string                          // 相关角色ID（经验奖励时使用）
  characterInfo?: {                             // 角色详细信息（用于展示）
    name: string                                // 角色姓名
    portrait: string                            // 角色头像
    type: 'Normal' | 'Rare' | 'Special'        // 角色类型
    level: number                               // 角色等级
  }
  amount: number                                // 数量
  materialType?: string                         // 材料类型（材料奖励时使用）
  source: string                                // 来源描述
  workshopType?: string                         // 工坊类型（派遣任务产生的产出）
  activity?: string                             // 活动类型（派遣任务产生的产出）
  day: number                                   // 产生的游戏天数
  timeSlot: string                              // 产生的时间段
  timestamp: number                             // 时间戳
  settled: boolean                              // 是否已结算
}

// 每日经营详情类型
export interface DailyOperationDetail {
  day: number
  income: {
    money: number
    materials: Record<string, number>
    experience: Record<string, number>
  }
  expenses: {
    money: number
    materials: Record<string, number>
    energy: number
  }
  activities: string[]                          // 当日活动记录
}

// 产出队列模块状态
export function createProductionQueueModule() {
  // 产出队列
  const productionQueue = ref<ProductionQueueItem[]>([])

  // 每日经营详情历史
  const dailyOperationHistory = ref<DailyOperationDetail[]>([])

  // 计算属性：待结算的产出队列
  const pendingQueue = computed(() =>
    productionQueue.value.filter(item => !item.settled)
  )

  // 计算属性：待结算金币总数
  const pendingMoney = computed(() =>
    pendingQueue.value
      .filter(item => item.type === 'money')
      .reduce((sum, item) => sum + item.amount, 0)
  )

  // 计算属性：待结算材料种类数
  const pendingMaterialsCount = computed(() => {
    const materials = new Set(
      pendingQueue.value
        .filter(item => item.type === 'material')
        .map(item => item.materialType)
    )
    return materials.size
  })

  // 计算属性：待结算经验的角色数
  const pendingExperienceCount = computed(() => {
    const characters = new Set(
      pendingQueue.value
        .filter(item => item.type === 'experience')
        .map(item => item.characterId)
    )
    return characters.size
  })

  // 生成唯一ID
  function generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  }

  // 添加到产出队列
  function addToProductionQueue(item: {
    type: 'material' | 'money' | 'experience'
    characterId?: string
    characterInfo?: {
      name: string
      portrait: string
      type: 'Normal' | 'Rare' | 'Special'
      level: number
    }
    amount: number
    materialType?: string
    source: string
    workshopType?: string
    activity?: string
  }, currentDay: number, currentTimeSlot: string) {
    const queueItem: ProductionQueueItem = {
      id: generateId(),
      ...item,
      day: currentDay,
      timeSlot: currentTimeSlot,
      timestamp: Date.now(),
      settled: false
    }

    productionQueue.value.push(queueItem)
  }

  // 批量添加到产出队列
  function addBatchToProductionQueue(items: Array<{
    type: 'material' | 'money' | 'experience'
    characterId?: string
    characterInfo?: {
      name: string
      portrait: string
      type: 'Normal' | 'Rare' | 'Special'
      level: number
    }
    amount: number
    materialType?: string
    source: string
    workshopType?: string
    activity?: string
  }>, currentDay: number, currentTimeSlot: string) {
    items.forEach(item => {
      addToProductionQueue(item, currentDay, currentTimeSlot)
    })
  }

  // 处理产出队列（在寝室休息时调用）
  function processProductionQueue(): {
    money: number
    materials: Record<string, number>
    experience: Record<string, number>
    processedItems: ProductionQueueItem[]
  } {
    const result = {
      money: 0,
      materials: {} as Record<string, number>,
      experience: {} as Record<string, number>,
      processedItems: [] as ProductionQueueItem[]
    }

    // 处理未结算的项目
    const unprocessedItems = productionQueue.value.filter(item => !item.settled)

    unprocessedItems.forEach(item => {
      switch (item.type) {
        case 'money':
          result.money += item.amount
          break
        case 'material':
          if (item.materialType) {
            result.materials[item.materialType] = (result.materials[item.materialType] || 0) + item.amount
          }
          break
        case 'experience':
          if (item.characterId) {
            result.experience[item.characterId] = (result.experience[item.characterId] || 0) + item.amount
          }
          break
      }

      // 标记为已结算
      item.settled = true
      result.processedItems.push(item)
    })

    return result
  }

  // 获取指定天数的产出记录
  function getDayProduction(day: number): ProductionQueueItem[] {
    return productionQueue.value.filter(item => item.day === day)
  }

  // 获取指定角色的产出记录
  function getCharacterProduction(characterId: string): ProductionQueueItem[] {
    return productionQueue.value.filter(item => item.characterId === characterId)
  }

  // 获取指定来源的产出记录
  function getProductionBySource(source: string): ProductionQueueItem[] {
    return productionQueue.value.filter(item => item.source === source)
  }

  // 记录每日经营详情
  function recordDailyOperation(day: number, income: DailyOperationDetail['income'], expenses: DailyOperationDetail['expenses'], activities: string[]) {
    const existingIndex = dailyOperationHistory.value.findIndex(detail => detail.day === day)

    const operationDetail: DailyOperationDetail = {
      day,
      income,
      expenses,
      activities
    }

    if (existingIndex !== -1) {
      // 更新现有记录
      dailyOperationHistory.value[existingIndex] = operationDetail
    } else {
      // 添加新记录
      dailyOperationHistory.value.push(operationDetail)
    }
  }

  // 获取每日经营详情
  function getDailyOperation(day: number): DailyOperationDetail | undefined {
    return dailyOperationHistory.value.find(detail => detail.day === day)
  }

  // 获取经营统计（指定天数范围）
  function getOperationStats(days: number = 7): {
    totalIncome: number
    totalExpenses: number
    netProfit: number
    avgDailyIncome: number
  } {
    const recentOperations = dailyOperationHistory.value.slice(-days)

    const totalIncome = recentOperations.reduce((sum, op) => sum + op.income.money, 0)
    const totalExpenses = recentOperations.reduce((sum, op) => sum + op.expenses.money, 0)
    const netProfit = totalIncome - totalExpenses
    const avgDailyIncome = recentOperations.length > 0 ? totalIncome / recentOperations.length : 0

    return {
      totalIncome,
      totalExpenses,
      netProfit,
      avgDailyIncome
    }
  }

  // 清理历史记录（保留最近30天）
  function cleanupHistory(currentDay: number) {
    const cutoffDay = currentDay - 30

    // 清理产出队列（保留已结算的记录用于统计）
    productionQueue.value = productionQueue.value.filter(item =>
      item.day > cutoffDay || !item.settled
    )

    // 清理每日经营详情
    dailyOperationHistory.value = dailyOperationHistory.value.filter(detail =>
      detail.day > cutoffDay
    )
  }

  // 重置产出队列
  function resetProductionQueue() {
    productionQueue.value = []
    dailyOperationHistory.value = []
  }

  return {
    // 状态
    productionQueue,
    dailyOperationHistory,

    // 计算属性
    pendingQueue,
    pendingMoney,
    pendingMaterialsCount,
    pendingExperienceCount,

    // 方法
    addToProductionQueue,
    addBatchToProductionQueue,
    processProductionQueue,
    getDayProduction,
    getCharacterProduction,
    getProductionBySource,
    recordDailyOperation,
    getDailyOperation,
    getOperationStats,
    cleanupHistory,
    resetProductionQueue
  }
}

export type ProductionQueueModule = ReturnType<typeof createProductionQueueModule>
