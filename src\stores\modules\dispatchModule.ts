import { ref } from 'vue'
import type { TimeSlot } from '../gameStore'

// 派遣队列项目类型
export interface DispatchQueueItem {
  characterId: string
  workshopType: string
  activity: string
  startDay: number
  startTimeSlot: string
}

// 派遣模块状态
export function createDispatchModule() {
  // 派遣队列
  const dispatchQueue = ref<DispatchQueueItem[]>([])

  // 添加角色到派遣队列
  function addToDispatchQueue(characterId: string, workshopType: string, activity: string, currentDay: number, currentTimeSlot: TimeSlot) {
    const existingIndex = dispatchQueue.value.findIndex(item => item.characterId === characterId)

    if (existingIndex !== -1) {
      // 如果角色已在队列中，更新信息
      dispatchQueue.value[existingIndex] = {
        characterId,
        workshopType,
        activity,
        startDay: currentDay,
        startTimeSlot: currentTimeSlot
      }
    } else {
      // 添加新的派遣记录
      dispatchQueue.value.push({
        characterId,
        workshopType,
        activity,
        startDay: currentDay,
        startTimeSlot: currentTimeSlot
      })
    }
  }

  // 从派遣队列中移除角色
  function removeFromDispatchQueue(characterId: string) {
    const index = dispatchQueue.value.findIndex(item => item.characterId === characterId)
    if (index !== -1) {
      dispatchQueue.value.splice(index, 1)
    }
  }

  // 清空派遣队列
  function clearDispatchQueue() {
    dispatchQueue.value = []
  }

  // 结束所有派遣（在推进到夜晚时调用）
  function endAllDispatches() {
    // 这个函数需要与characterStore协调，在组件中调用
    // 将所有派遣中的角色状态重置为空闲
    return dispatchQueue.value.length
  }

  // 获取角色的派遣信息
  function getCharacterDispatch(characterId: string): DispatchQueueItem | undefined {
    return dispatchQueue.value.find(item => item.characterId === characterId)
  }

  // 检查角色是否在派遣中
  function isCharacterDispatched(characterId: string): boolean {
    return dispatchQueue.value.some(item => item.characterId === characterId)
  }

  // 获取指定工坊的派遣角色数量
  function getWorkshopDispatchCount(workshopType: string): number {
    return dispatchQueue.value.filter(item => item.workshopType === workshopType).length
  }

  // 获取所有派遣中的角色ID列表
  function getDispatchedCharacterIds(): string[] {
    return dispatchQueue.value.map(item => item.characterId)
  }

  return {
    // 状态
    dispatchQueue,

    // 方法
    addToDispatchQueue,
    removeFromDispatchQueue,
    clearDispatchQueue,
    endAllDispatches,
    getCharacterDispatch,
    isCharacterDispatched,
    getWorkshopDispatchCount,
    getDispatchedCharacterIds
  }
}

export type DispatchModule = ReturnType<typeof createDispatchModule>
