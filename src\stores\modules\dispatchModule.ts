import { ref, computed } from 'vue'
import type { TimeSlot } from '../gameStore'

// 派遣队列项目类型
export interface DispatchQueueItem {
  characterId: string
  workshopType: string
  activity: string
  startDay: number
  startTimeSlot: string
  // 新增：预计产出信息
  expectedOutput?: {
    materials?: Record<string, number>  // 材料产出
    money?: number                      // 金币产出
  }
  // 新增：角色消耗信息
  characterCost?: {
    stamina: number                     // 体力消耗
    mood: number                        // 心情消耗
  }
}

// 工坊派遣汇总信息
export interface WorkshopDispatchSummary {
  workshopType: string
  characterCount: number
  characters: string[]                  // 角色ID列表
  totalExpectedOutput: {
    materials?: Record<string, number>
    money?: number
  }
  totalCharacterCost: {
    stamina: number
    mood: number
  }
}

// 派遣模块状态
export function createDispatchModule() {
  // 派遣队列
  const dispatchQueue = ref<DispatchQueueItem[]>([])

  // 计算属性：按工坊分组的派遣汇总
  const workshopDispatchSummaries = computed((): WorkshopDispatchSummary[] => {
    const summaryMap = new Map<string, WorkshopDispatchSummary>()

    dispatchQueue.value.forEach(item => {
      if (!summaryMap.has(item.workshopType)) {
        summaryMap.set(item.workshopType, {
          workshopType: item.workshopType,
          characterCount: 0,
          characters: [],
          totalExpectedOutput: {},
          totalCharacterCost: { stamina: 0, mood: 0 }
        })
      }

      const summary = summaryMap.get(item.workshopType)!
      summary.characterCount++
      summary.characters.push(item.characterId)

      // 累计预计产出
      if (item.expectedOutput) {
        if (item.expectedOutput.money) {
          summary.totalExpectedOutput.money = (summary.totalExpectedOutput.money || 0) + item.expectedOutput.money
        }
        if (item.expectedOutput.materials) {
          if (!summary.totalExpectedOutput.materials) {
            summary.totalExpectedOutput.materials = {}
          }
          Object.entries(item.expectedOutput.materials).forEach(([material, amount]) => {
            summary.totalExpectedOutput.materials![material] = (summary.totalExpectedOutput.materials![material] || 0) + amount
          })
        }
      }

      // 累计角色消耗
      if (item.characterCost) {
        summary.totalCharacterCost.stamina += item.characterCost.stamina
        summary.totalCharacterCost.mood += item.characterCost.mood
      }
    })

    return Array.from(summaryMap.values())
  })

  // 添加角色到派遣队列（增强版）
  function addToDispatchQueue(
    characterId: string,
    workshopType: string,
    activity: string,
    currentDay: number,
    currentTimeSlot: TimeSlot,
    expectedOutput?: { materials?: Record<string, number>, money?: number },
    characterCost?: { stamina: number, mood: number }
  ) {
    const existingIndex = dispatchQueue.value.findIndex(item => item.characterId === characterId)

    const dispatchItem: DispatchQueueItem = {
      characterId,
      workshopType,
      activity,
      startDay: currentDay,
      startTimeSlot: currentTimeSlot,
      expectedOutput,
      characterCost
    }

    if (existingIndex !== -1) {
      // 如果角色已在队列中，更新信息
      dispatchQueue.value[existingIndex] = dispatchItem
    } else {
      // 添加新的派遣记录
      dispatchQueue.value.push(dispatchItem)
    }
  }

  // 从派遣队列中移除角色
  function removeFromDispatchQueue(characterId: string): boolean {
    const index = dispatchQueue.value.findIndex(item => item.characterId === characterId)
    if (index !== -1) {
      dispatchQueue.value.splice(index, 1)
      return true
    }
    return false
  }

  // 清空派遣队列
  function clearDispatchQueue() {
    dispatchQueue.value = []
  }

  // 获取角色的派遣信息
  function getCharacterDispatch(characterId: string): DispatchQueueItem | undefined {
    return dispatchQueue.value.find(item => item.characterId === characterId)
  }

  // 检查角色是否在派遣中
  function isCharacterDispatched(characterId: string): boolean {
    return dispatchQueue.value.some(item => item.characterId === characterId)
  }

  // 获取指定工坊的派遣角色数量
  function getWorkshopDispatchCount(workshopType: string): number {
    return dispatchQueue.value.filter(item => item.workshopType === workshopType).length
  }

  // 获取指定工坊的派遣角色ID列表
  function getWorkshopDispatchedCharacters(workshopType: string): string[] {
    return dispatchQueue.value
      .filter(item => item.workshopType === workshopType)
      .map(item => item.characterId)
  }

  // 获取所有派遣中的角色ID列表
  function getDispatchedCharacterIds(): string[] {
    return dispatchQueue.value.map(item => item.characterId)
  }

  // 获取工坊派遣汇总信息
  function getWorkshopSummary(workshopType: string): WorkshopDispatchSummary | undefined {
    return workshopDispatchSummaries.value.find(summary => summary.workshopType === workshopType)
  }

  // 结束所有派遣（在推进到夜晚时调用）
  // 返回派遣数据以便生成产出队列
  function endAllDispatches(): DispatchQueueItem[] {
    const completedDispatches = [...dispatchQueue.value] // 复制当前派遣队列
    clearDispatchQueue() // 清空派遣队列
    return completedDispatches
  }

  // 批量更新派遣项目的预计产出
  function updateDispatchExpectedOutput(characterId: string, expectedOutput: { materials?: Record<string, number>, money?: number }) {
    const dispatch = getCharacterDispatch(characterId)
    if (dispatch) {
      dispatch.expectedOutput = expectedOutput
    }
  }

  // 批量更新派遣项目的角色消耗
  function updateDispatchCharacterCost(characterId: string, characterCost: { stamina: number, mood: number }) {
    const dispatch = getCharacterDispatch(characterId)
    if (dispatch) {
      dispatch.characterCost = characterCost
    }
  }

  return {
    // 状态
    dispatchQueue,

    // 计算属性
    workshopDispatchSummaries,

    // 方法
    addToDispatchQueue,
    removeFromDispatchQueue,
    clearDispatchQueue,
    endAllDispatches,
    getCharacterDispatch,
    isCharacterDispatched,
    getWorkshopDispatchCount,
    getWorkshopDispatchedCharacters,
    getDispatchedCharacterIds,
    getWorkshopSummary,
    updateDispatchExpectedOutput,
    updateDispatchCharacterCost
  }
}

export type DispatchModule = ReturnType<typeof createDispatchModule>
