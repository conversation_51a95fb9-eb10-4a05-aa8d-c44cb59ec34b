<template>
  <div class="main-scene">
    <!-- 顶部状态栏 - 使用Affix固定 -->
    <a-affix :offset-top="0">
      <div class="top-status-bar">
        <!-- 左侧：时间显示 -->
        <div class="status-left">
          <div class="time-display" :class="{ compact: isMobile }" @click="isMobile && toggleTimeDetail()">
            <ClockCircleOutlined />
            <span v-if="!isMobile || showTimeDetail">第{{ gameStore.currentDay }}天 {{ gameStore.fullTimeDisplay }}</span>
            <span v-else>第{{ gameStore.currentDay }}天</span>
          </div>
        </div>

        <!-- 右侧：资源显示 -->
        <div class="status-right">
          <div class="resource-display" :class="{ compact: isMobile }" @click="isMobile && toggleResourceDetail()">
            <a-space :size="isMobile ? 'small' : 'large'">
              <div class="resource-item" v-if="!isMobile || showResourceDetail">
                <span class="resource-icon">💰</span>
                <span class="resource-value">{{ gameStore.playerResources.money }}</span>
              </div>
              <div class="resource-item" v-if="!isMobile || showResourceDetail">
                <span class="resource-icon">💎</span>
                <span class="resource-value">{{ gameStore.playerResources.gems }}</span>
              </div>
              <div class="resource-item" v-if="!isMobile || showResourceDetail">
                <span class="resource-icon">⚡</span>
                <span class="resource-value">{{ gameStore.playerResources.energy }}/{{
                  gameStore.playerResources.maxEnergy }}</span>
              </div>

              <!-- 产出队列指示器 -->
              <div class="resource-item production-queue-indicator"
                v-if="hasPendingProduction && (!isMobile || showResourceDetail)"
                @click.stop="showProductionDrawer = true">
                <a-tooltip title="点击查看待结算产出">
                  <ClockCircleOutlined class="loading-icon" spin />
                  <a-badge :count="gameStore.pendingQueue.length" :offset="[10, -5]">
                    <span class="resource-value">产出</span>
                  </a-badge>
                </a-tooltip>
              </div>

              <!-- 移动端简化显示 -->
              <div class="resource-item" v-if="isMobile && !showResourceDetail">
                <span class="resource-icon">💰</span>
                <span class="resource-value">{{ gameStore.playerResources.money }}</span>
                <ClockCircleOutlined v-if="hasPendingProduction" class="loading-icon-small" spin />
                <MoreOutlined class="more-icon" />
              </div>
            </a-space>
          </div>
        </div>
      </div>
    </a-affix>

    <!-- 主内容区域 -->
    <div class="game-content">
      <div class="content-wrapper">
        <!-- 根据当前选择的菜单显示不同内容 -->
        <component :is="currentComponent" />
      </div>
    </div>

    <!-- 底部导航栏 - 使用Affix固定 -->
    <a-affix :offset-bottom="0">
      <div class="bottom-navigation">
        <!-- 桌面端：显示所有按钮 -->
        <div v-if="!isMobile" class="nav-buttons-desktop">
          <a-space size="large">
            <a-button v-for="item in navigationItems" :key="item.key"
              :type="selectedKeys[0] === item.key ? 'primary' : 'default'" @click="handleNavClick(item.key)"
              class="nav-button">
              <template #icon>
                <component :is="item.icon" />
              </template>
              {{ item.label }}
            </a-button>
          </a-space>
        </div>

        <!-- 移动端：收纳按钮 -->
        <div v-else class="nav-buttons-mobile">
          <!-- 收纳的导航菜单 -->
          <div v-if="showMobileNav" class="mobile-nav-panel">
            <div class="nav-grid">
              <div v-for="item in navigationItems" :key="item.key" class="nav-item"
                :class="{ active: selectedKeys[0] === item.key }" @click="handleNavClick(item.key)">
                <component :is="item.icon" class="nav-icon" />
                <span class="nav-label">{{ item.label }}</span>
              </div>
            </div>
          </div>

          <!-- 收纳按钮 -->
          <a-button type="primary" shape="circle" size="large" class="mobile-nav-toggle" @click="toggleMobileNav">
            <MenuOutlined v-if="!showMobileNav" />
            <CloseOutlined v-else />
          </a-button>
        </div>
      </div>
    </a-affix>

    <!-- 设置弹窗 -->
    <a-modal v-model:open="settingsVisible" title="游戏设置" :footer="null" width="600px" centered>
      <SettingsPanel @close="settingsVisible = false" />
    </a-modal>

    <!-- 退出确认弹窗 -->
    <a-modal v-model:open="exitConfirmVisible" title="退出游戏" centered @ok="confirmExit"
      @cancel="exitConfirmVisible = false">
      <p>确定要退出游戏吗？未保存的进度将会丢失。</p>
    </a-modal>

    <!-- 产出队列抽屉 -->
    <a-drawer v-model:open="showProductionDrawer" title="工坊产出与派遣状态" placement="right" :width="isMobile ? '100%' : 520"
      :closable="true">
      <div class="production-drawer-content">
        <!-- 总体概览 -->
        <div class="queue-overview">
          <a-statistic-group>
            <a-row :gutter="16">
              <a-col :span="6">
                <a-statistic title="待结算产出" :value="gameStore.pendingQueue.length" suffix="项" />
              </a-col>
              <a-col :span="6">
                <a-statistic title="金币收入" :value="gameStore.pendingMoney" suffix="💰" />
              </a-col>
              <a-col :span="6">
                <a-statistic title="材料种类" :value="gameStore.pendingMaterialsCount" suffix="种" />
              </a-col>
              <a-col :span="6">
                <a-statistic title="经验奖励" :value="gameStore.pendingExperienceCount" suffix="人" />
              </a-col>
            </a-row>
          </a-statistic-group>
        </div>

        <!-- 工坊分组显示 -->
        <div class="workshop-sections">
          <a-empty v-if="!hasActiveWorkshops" description="暂无活跃工坊" />

          <div v-else class="workshop-groups">
            <div v-for="summary in activeWorkshopSummaries" :key="summary.workshopType" class="workshop-section">
              <div class="workshop-header">
                <div class="workshop-title">
                  <span class="workshop-icon">{{ getWorkshopIcon(summary.workshopType) }}</span>
                  <h4>{{ getWorkshopName(summary.workshopType) }}</h4>
                  <a-tag color="blue" size="small">{{ summary.characterCount }}人派遣中</a-tag>
                </div>
                <a-button size="small" type="text" @click="toggleWorkshopDetail(summary.workshopType)">
                  {{ expandedWorkshops.includes(summary.workshopType) ? '收起' : '展开' }}
                </a-button>
              </div>

              <!-- 工坊汇总信息 -->
              <div class="workshop-summary">
                <div class="summary-row">
                  <div class="summary-item" v-if="summary.totalExpectedOutput?.money">
                    <span class="summary-label">预计金币:</span>
                    <span class="summary-value money">+{{ summary.totalExpectedOutput.money }}💰</span>
                  </div>
                  <div class="summary-item" v-if="summary.totalExpectedOutput?.materials">
                    <span class="summary-label">预计材料:</span>
                    <span class="summary-value materials">
                      <span v-for="(amount, material) in summary.totalExpectedOutput.materials" :key="material">
                        {{ material }}+{{ amount }}
                      </span>
                    </span>
                  </div>
                  <div class="summary-item">
                    <span class="summary-label">消耗:</span>
                    <span class="summary-value cost">
                      体力{{ summary.totalCharacterCost?.stamina || 0 }} 心情{{ summary.totalCharacterCost?.mood || 0 }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 详细信息（可展开） -->
              <div v-if="expandedWorkshops.includes(summary.workshopType)" class="workshop-details">
                <!-- 派遣角色列表 -->
                <div class="dispatched-characters">
                  <h5>派遣角色</h5>
                  <div class="character-grid">
                    <div v-for="character in getWorkshopDispatchedCharacters(summary.workshopType)"
                         :key="character.id" class="character-card">
                      <a-avatar :src="getCharacterPortraitUrl(character.portrait)" :size="40">
                        {{ character.name.charAt(0) }}
                      </a-avatar>
                      <div class="character-info">
                        <div class="character-name">{{ character.name }}</div>
                        <a-tag :color="getCharacterTypeColor(character.type)" size="small">
                          {{ getCharacterTypeText(character.type) }}
                        </a-tag>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 产出详情 -->
                <div class="production-details">
                  <h5>产出详情</h5>
                  <div class="production-items">
                    <div v-for="item in getWorkshopProductionItems(summary.workshopType)"
                         :key="item.id" class="production-item">
                      <div class="item-icon">{{ getProductionIcon(item) }}</div>
                      <div class="item-info">
                        <div class="item-name">{{ getProductionName(item) }}</div>
                        <div class="item-amount">+{{ item.amount }}</div>
                        <div class="item-time">第{{ item.day }}天 {{ item.timeSlot }}</div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 经验奖励详情 -->
                <div class="experience-details" v-if="getWorkshopExperienceRewards(summary.workshopType).length > 0">
                  <h5>经验奖励</h5>
                  <div class="experience-items">
                    <div v-for="reward in getWorkshopExperienceRewards(summary.workshopType)"
                         :key="reward.id" class="experience-item">
                      <a-avatar :src="getCharacterPortraitUrl(reward.characterInfo.portrait)" :size="32">
                        {{ reward.characterInfo.name.charAt(0) }}
                      </a-avatar>
                      <div class="reward-info">
                        <div class="character-name">{{ reward.characterInfo.name }}</div>
                        <div class="experience-amount">+{{ reward.amount }} 经验</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="drawer-actions">
          <a-space>
            <a-button @click="showProductionDrawer = false">关闭</a-button>
            <a-button type="primary" :disabled="gameStore.pendingQueue.length === 0" @click="goToBedroom">
              前往寝室结算
            </a-button>
          </a-space>
        </div>
      </div>
    </a-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  EnvironmentOutlined,
  TeamOutlined,
  ShoppingOutlined,
  CalendarOutlined,
  BranchesOutlined,
  SettingOutlined,
  LogoutOutlined,
  ClockCircleOutlined,
  MoreOutlined,
  MenuOutlined,
  CloseOutlined,
} from '@ant-design/icons-vue'

// 组件导入
import SettingsPanel from '@/components/SettingsPanel.vue'
import GameMap from '@/components/GameMap.vue'
import { useGameStore } from '@/stores/gameStore'

// 临时占位组件
const PlaceholderComponent = {
  template: '<div class="placeholder">该功能正在开发中...</div>'
}

const router = useRouter()
const gameStore = useGameStore()

// 响应式数据
const selectedKeys = ref(['map'])
const settingsVisible = ref(false)
const exitConfirmVisible = ref(false)

// 移动端相关状态
const isMobile = ref(false)
const showMobileNav = ref(false)
const showTimeDetail = ref(false)
const showResourceDetail = ref(false)

// 产出队列相关状态
const showProductionDrawer = ref(false)
const expandedWorkshops = ref<string[]>([]) // 展开的工坊列表

// 导航项配置
const navigationItems = [
  { key: 'map', label: '游戏地图', icon: EnvironmentOutlined },
  { key: 'characters', label: '角色列表', icon: TeamOutlined },
  { key: 'inventory', label: '库存', icon: ShoppingOutlined },
  { key: 'calendar', label: '日历', icon: CalendarOutlined },
  { key: 'development', label: '发展树', icon: BranchesOutlined },
  { key: 'settings', label: '设置', icon: SettingOutlined },
  { key: 'exit', label: '退出游戏', icon: LogoutOutlined },
]

// 组件映射
const componentMap = {
  map: GameMap,
  characters: PlaceholderComponent,
  inventory: PlaceholderComponent,
  calendar: PlaceholderComponent,
  development: PlaceholderComponent,
}

// 当前显示的组件
const currentComponent = computed(() => {
  return componentMap[selectedKeys.value[0] as keyof typeof componentMap] || GameMap
})

// 产出队列相关计算属性
const hasPendingProduction = computed(() => {
  return gameStore.pendingQueue.length > 0
})

// 活跃工坊汇总
const activeWorkshopSummaries = computed(() => {
  return gameStore.workshopDispatchSummaries.filter(summary => summary.characterCount > 0)
})

// 是否有活跃工坊
const hasActiveWorkshops = computed(() => {
  return activeWorkshopSummaries.value.length > 0
})

// 获取工坊派遣的角色
const getWorkshopDispatchedCharacters = (workshopType: string) => {
  return gameStore.getWorkshopDispatchedCharacters(workshopType)
}

// 获取工坊的产出项目
const getWorkshopProductionItems = (workshopType: string) => {
  return gameStore.pendingQueue.filter(item => item.workshopType === workshopType)
}

// 获取工坊的经验奖励
const getWorkshopExperienceRewards = (workshopType: string) => {
  return gameStore.pendingExperienceRewards.filter(reward => reward.workshopType === workshopType)
}

// 获取工坊图标
const getWorkshopIcon = (workshopType: string): string => {
  const icons: Record<string, string> = {
    hall: '🎭',
    'holy-water': '💧',
    honey: '🍯',
    milk: '🥛',
    nectar: '🍷'
  }
  return icons[workshopType] || '🏭'
}

// 切换工坊详情展开状态
const toggleWorkshopDetail = (workshopType: string) => {
  const index = expandedWorkshops.value.indexOf(workshopType)
  if (index > -1) {
    expandedWorkshops.value.splice(index, 1)
  } else {
    expandedWorkshops.value.push(workshopType)
  }
}

// 检测移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 切换移动端导航
const toggleMobileNav = () => {
  showMobileNav.value = !showMobileNav.value
}

// 切换时间详情显示
const toggleTimeDetail = () => {
  showTimeDetail.value = !showTimeDetail.value
  setTimeout(() => {
    showTimeDetail.value = false
  }, 3000)
}

// 切换资源详情显示
const toggleResourceDetail = () => {
  showResourceDetail.value = !showResourceDetail.value
  setTimeout(() => {
    showResourceDetail.value = false
  }, 3000)
}

// 导航点击处理
const handleNavClick = (key: string) => {
  if (key === 'settings') {
    settingsVisible.value = true
    showMobileNav.value = false
    return
  }

  if (key === 'exit') {
    exitConfirmVisible.value = true
    showMobileNav.value = false
    return
  }

  if (key === 'characters') {
    router.push('/characters')
    showMobileNav.value = false
    return
  }

  selectedKeys.value = [key]
  showMobileNav.value = false
}

// 产出队列相关方法
const getProductionTypeColor = (type: string) => {
  switch (type) {
    case 'money': return 'gold'
    case 'material': return 'green'
    case 'experience': return 'blue'
    default: return 'default'
  }
}

const getProductionTypeText = (type: string) => {
  switch (type) {
    case 'money': return '金币'
    case 'material': return '材料'
    case 'experience': return '经验'
    default: return '未知'
  }
}

const getProductionIcon = (item: any) => {
  switch (item.type) {
    case 'money': return '💰'
    case 'material': return getMaterialIcon(item.materialType)
    case 'experience': return '✨'
    default: return '❓'
  }
}

const getMaterialIcon = (materialType: string) => {
  const icons: Record<string, string> = {
    seeds: '🌱',
    fruits: '🍎',
    water: '💧',
    honey: '🍯',
    milk: '🥛',
    nectar: '🧪'
  }
  return icons[materialType] || '📦'
}

const getProductionName = (item: any) => {
  switch (item.type) {
    case 'money': return '金币'
    case 'material': return getMaterialName(item.materialType)
    case 'experience': return '经验值'
    default: return '未知物品'
  }
}

const getMaterialName = (materialType: string) => {
  const names: Record<string, string> = {
    seeds: '种子',
    fruits: '灵果',
    water: '圣水',
    honey: '蜜酿',
    milk: '乳液',
    nectar: '琼浆'
  }
  return names[materialType] || materialType
}

// 角色相关方法
const getCharacterPortraitUrl = (portrait: string) => {
  // 如果是完整URL，直接返回
  if (portrait.startsWith('http')) {
    return portrait
  }
  // 否则构建本地路径
  return `/src/assets/graphics/portraits/${portrait}`
}

const getCharacterTypeColor = (type: string) => {
  switch (type) {
    case 'Normal': return 'default'
    case 'Rare': return 'blue'
    case 'Special': return 'purple'
    default: return 'default'
  }
}

const getCharacterTypeText = (type: string) => {
  switch (type) {
    case 'Normal': return '普通'
    case 'Rare': return '稀有'
    case 'Special': return '特殊'
    default: return '未知'
  }
}

const getWorkshopName = (workshopType: string) => {
  const names: Record<string, string> = {
    llyzf: '琉璃胭脂坊',
    hspmh: '黑市拍卖会',
    fcsh: '翡翠商会',
    gdy: '孤独园',
    farm: '农场'
  }
  return names[workshopType] || workshopType
}

const goToBedroom = () => {
  showProductionDrawer.value = false
  router.push('/bedroom')
}

// 确认退出
const confirmExit = () => {
  // 保存游戏
  gameStore.saveGame()
  message.success('游戏已保存')
  router.push('/')
}

onMounted(() => {
  // 尝试加载游戏数据，如果没有存档则使用默认状态
  const hasData = gameStore.loadGame()
  if (hasData) {
    message.success(`欢迎回来！当前进度：${gameStore.fullTimeDisplay}`)
  } else {
    message.success('欢迎来到琼浆玉液的世界！')
  }

  // 检测移动端
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<style scoped>
.main-scene {
  height: 100vh;
  background: var(--bg-primary);
}

/* 顶部状态栏 */
.top-status-bar {
  background: white;
  padding: 12px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.status-left,
.status-right {
  display: flex;
  align-items: center;
}

.time-display {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, var(--nectar-blue), var(--nectar-purple));
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.time-display:hover {
  transform: scale(1.05);
}

.time-display.compact {
  padding: 6px 12px;
  font-size: 14px;
}

.resource-display {
  background: rgba(0, 0, 0, 0.05);
  padding: 8px 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.resource-display:hover {
  background: rgba(0, 0, 0, 0.1);
}

.resource-display.compact {
  padding: 6px 12px;
}

.resource-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.resource-icon {
  font-size: 16px;
}

.resource-value {
  font-weight: bold;
  color: #333;
}

.more-icon {
  margin-left: 4px;
  color: #999;
}

/* 主内容区域 */
.game-content {
  background: var(--bg-primary);
  min-height: calc(100vh - 120px);
  padding-top: 20px;
  padding-bottom: 80px;
}

.content-wrapper {
  padding: 0 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  background: white;
  border-radius: 12px;
  font-size: 18px;
  color: #999;
}

/* 底部导航栏 */
.bottom-navigation {
  background: white;
  padding: 16px 20px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.nav-buttons-desktop {
  display: flex;
  justify-content: center;
}

.nav-button {
  height: 48px;
  border-radius: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.nav-button:hover {
  transform: translateY(-2px);
}

/* 移动端导航 */
.nav-buttons-mobile {
  position: relative;
  display: flex;
  justify-content: flex-end;
}

.mobile-nav-panel {
  position: absolute;
  bottom: 70px;
  right: 0;
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  min-width: 280px;
}

.nav-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px 8px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.nav-item:hover {
  background: #e9ecef;
  transform: translateY(-2px);
}

.nav-item.active {
  background: var(--nectar-purple);
  color: white;
}

/* 产出队列相关样式 */
.production-queue-indicator {
  cursor: pointer;
  transition: all 0.3s ease;
}

.production-queue-indicator:hover {
  background: rgba(24, 144, 255, 0.1);
  border-radius: 4px;
}

.loading-icon {
  color: #1890ff;
  margin-right: 4px;
}

.loading-icon-small {
  color: #1890ff;
  margin-left: 4px;
  font-size: 12px;
}

.production-drawer-content {
  padding: 16px 0;
}

.queue-overview {
  margin-bottom: 24px;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 8px;
}

/* 工坊分组样式 */
.workshop-sections {
  max-height: 500px;
  overflow-y: auto;
}

.workshop-groups {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.workshop-section {
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.workshop-section:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.1);
}

.workshop-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.workshop-title {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.workshop-title h4 {
  margin: 0;
  color: #262626;
  font-weight: 600;
}

.workshop-summary {
  padding: 12px 16px;
  background: #f9f9f9;
}

.summary-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.summary-label {
  color: #666;
  font-weight: 500;
}

.summary-value.money {
  color: #faad14;
  font-weight: 600;
}

.summary-value.materials {
  color: #52c41a;
  font-weight: 600;
}

.summary-value.cost {
  color: #f5222d;
  font-weight: 600;
}

/* 工坊详情样式 */
.workshop-details {
  padding: 16px;
  background: white;
}

.workshop-details h5 {
  margin: 0 0 12px 0;
  color: #262626;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.character-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
  margin-bottom: 20px;
}

.character-card {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.character-info {
  flex: 1;
}

.character-info .character-name {
  font-size: 12px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.production-details,
.experience-details {
  margin-bottom: 20px;
}

.production-items,
.experience-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.production-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
}

.experience-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 6px;
}

.item-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.item-info,
.reward-info {
  flex: 1;
}

.item-name,
.character-name {
  font-size: 12px;
  font-weight: 500;
  color: #262626;
}

.item-amount,
.experience-amount {
  font-size: 11px;
  color: #52c41a;
  font-weight: 600;
}

.item-time {
  font-size: 10px;
  color: #8c8c8c;
}

.item-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.item-amount {
  color: #595959;
  font-size: 14px;
  margin-bottom: 2px;
}

.item-meta {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #8c8c8c;
}

.item-time {
  color: #8c8c8c;
  font-size: 12px;
}

.workshop-info {
  color: #595959;
  font-weight: 500;
}

.drawer-actions {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  text-align: right;
}

.nav-icon {
  font-size: 24px;
}

.nav-label {
  font-size: 12px;
  font-weight: 500;
  text-align: center;
}

.mobile-nav-toggle {
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .top-status-bar {
    padding: 8px 16px;
  }

  .content-wrapper {
    padding: 0 16px;
  }

  .bottom-navigation {
    padding: 12px 16px;
  }

  .nav-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .mobile-nav-panel {
    right: 16px;
    min-width: 240px;
  }

  .character-grid {
    grid-template-columns: 1fr;
  }

  .summary-row {
    flex-direction: column;
    gap: 8px;
  }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
  .time-display {
    padding: 4px 8px;
    font-size: 12px;
  }

  .resource-display {
    padding: 4px 8px;
  }

  .resource-icon {
    font-size: 14px;
  }

  .resource-value {
    font-size: 12px;
  }
}
</style>
