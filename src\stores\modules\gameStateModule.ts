import type { TimeModule } from './timeModule'
import type { DispatchModule } from './dispatchModule'
import type { PlayerStatsModule } from './playerStatsModule'
import type { InventoryModule } from './inventoryModule'
import type { ProductionQueueModule } from './productionQueueModule'

export interface GameStateModule {
  // 方法
  saveGame: (
    timeModule: TimeModule,
    dispatchModule: DispatchModule,
    playerStatsModule: PlayerStatsModule,
    inventoryModule: InventoryModule,
    productionQueueModule: ProductionQueueModule
  ) => void
  loadGame: (
    timeModule: TimeModule,
    dispatchModule: DispatchModule,
    playerStatsModule: PlayerStatsModule,
    inventoryModule: InventoryModule,
    productionQueueModule: ProductionQueueModule
  ) => boolean
  resetGame: (
    timeModule: TimeModule,
    dispatchModule: DispatchModule,
    playerStatsModule: PlayerStatsModule,
    inventoryModule: InventoryModule,
    productionQueueModule: ProductionQueueModule
  ) => void
  advanceTime: (
    timeModule: TimeModule,
    dispatchModule: DispatchModule,
    playerStatsModule: PlayerStatsModule,
    inventoryModule: InventoryModule,
    productionQueueModule: ProductionQueueModule
  ) => { success: boolean; message: string }
}

export function createGameStateModule(): GameStateModule {
  // 保存游戏状态
  function saveGame(
    timeModule: TimeModule,
    dispatchModule: DispatchModule,
    playerStatsModule: PlayerStatsModule,
    inventoryModule: InventoryModule,
    productionQueueModule: ProductionQueueModule
  ) {
    const gameState = {
      currentDay: timeModule.currentDay.value,
      currentTimeSlot: timeModule.currentTimeSlot.value,
      playerResources: playerStatsModule.playerResources.value,
      materials: inventoryModule.materials.value,
      dispatchQueue: dispatchModule.dispatchQueue.value,
      productionQueue: productionQueueModule.productionQueue.value,
      savedAt: new Date().toISOString(),
    }

    localStorage.setItem('nectar-game-save', JSON.stringify(gameState))
  }

  // 加载游戏状态
  function loadGame(
    timeModule: TimeModule,
    dispatchModule: DispatchModule,
    playerStatsModule: PlayerStatsModule,
    inventoryModule: InventoryModule,
    productionQueueModule: ProductionQueueModule
  ): boolean {
    try {
      const saved = localStorage.getItem('nectar-game-save')
      if (saved) {
        const gameState = JSON.parse(saved)
        
        // 加载时间状态
        if (gameState.currentDay) {
          timeModule.setDay(gameState.currentDay)
        }
        if (gameState.currentTimeSlot) {
          timeModule.setTimeSlot(gameState.currentTimeSlot)
        }
        
        // 加载模块状态
        if (gameState.playerResources) {
          Object.assign(playerStatsModule.playerResources.value, gameState.playerResources)
        }
        if (gameState.materials) {
          Object.assign(inventoryModule.materials.value, gameState.materials)
        }
        if (gameState.dispatchQueue) {
          dispatchModule.dispatchQueue.value = gameState.dispatchQueue
        }
        if (gameState.productionQueue) {
          productionQueueModule.productionQueue.value = gameState.productionQueue
        }
        
        return true
      }
    } catch (error) {
      console.error('Failed to load game:', error)
    }
    return false
  }

  // 重置游戏状态
  function resetGame(
    timeModule: TimeModule,
    dispatchModule: DispatchModule,
    playerStatsModule: PlayerStatsModule,
    inventoryModule: InventoryModule,
    productionQueueModule: ProductionQueueModule
  ) {
    // 重置各模块
    timeModule.resetTime()
    playerStatsModule.resetPlayerResources()
    inventoryModule.resetInventory()
    dispatchModule.clearDispatchQueue()
    productionQueueModule.resetProductionQueue()

    // 清除存档
    localStorage.removeItem('nectar-game-save')
  }

  // 推进时间（协调各模块）
  function advanceTime(
    timeModule: TimeModule,
    dispatchModule: DispatchModule,
    playerStatsModule: PlayerStatsModule,
    inventoryModule: InventoryModule,
    productionQueueModule: ProductionQueueModule
  ): { success: boolean; message: string } {
    const currentSlot = timeModule.currentTimeSlot.value

    if (currentSlot === '上午') {
      return timeModule.advanceToAfternoon()
    } else if (currentSlot === '下午') {
      // 注意：下午到夜晚的派遣处理现在在gameStore中处理
      return timeModule.advanceToNight()
    } else {
      // 夜晚推进到下一天的上午
      const result = timeModule.advanceToNextDay()

      // 每日恢复体力
      playerStatsModule.restoreEnergyDaily(timeModule.currentDay.value)

      // 清空派遣队列（新的一天开始）
      dispatchModule.clearDispatchQueue()

      // 清理历史记录
      playerStatsModule.cleanupHistory(timeModule.currentDay.value)
      inventoryModule.cleanupHistory(timeModule.currentDay.value)
      productionQueueModule.cleanupHistory(timeModule.currentDay.value)

      return result
    }
  }

  return {
    saveGame,
    loadGame,
    resetGame,
    advanceTime
  }
}
