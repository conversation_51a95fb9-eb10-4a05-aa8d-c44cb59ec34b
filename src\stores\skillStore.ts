import { defineStore } from 'pinia'
import { ref } from 'vue'

export type SkillRarity = 'Common' | 'Rare'

export interface Skill {
  id: string
  name: string
  rarity: SkillRarity
  description: string
  effects: SkillEffect[]
}

export interface SkillEffect {
  type: 'production_efficiency' | 'mood_recovery' | 'stamina_recovery' | 'failure_reduction' | 'income_boost' | 'special'
  target: string // 目标工坊或效果范围
  value: number // 效果数值
  description: string
}

export const useSkillStore = defineStore('skill', () => {
  // 技能数据库
  const skillDatabase = ref<Skill[]>([
    // 常见技能
    {
      id: 'holy_water_mastery',
      name: '圣水专精',
      rarity: 'Common',
      description: '圣水生产效率 +15%',
      effects: [
        {
          type: 'production_efficiency',
          target: 'holy-water',
          value: 15,
          description: '圣水工坊生产效率提升15%'
        }
      ]
    },
    {
      id: 'honey_mastery',
      name: '蜜酿专精',
      rarity: 'Common',
      description: '蜜酿制作效率提升 15%，失败率降低',
      effects: [
        {
          type: 'production_efficiency',
          target: 'honey',
          value: 15,
          description: '蜜酿工坊生产效率提升15%'
        },
        {
          type: 'failure_reduction',
          target: 'honey',
          value: 10,
          description: '蜜酿工坊失败率降低10%'
        }
      ]
    },
    {
      id: 'performance_talent',
      name: '表演天赋',
      rarity: 'Common',
      description: '表演获得收益 +20%',
      effects: [
        {
          type: 'income_boost',
          target: 'performance',
          value: 20,
          description: '表演收益提升20%'
        }
      ]
    },
    {
      id: 'soul_comfort',
      name: '心灵慰藉',
      rarity: 'Common',
      description: '同工坊角色心情恢复速度 +5',
      effects: [
        {
          type: 'mood_recovery',
          target: 'workshop',
          value: 5,
          description: '同工坊其他角色心情恢复速度提升'
        }
      ]
    },
    {
      id: 'vigorous_stamina',
      name: '体力旺盛',
      rarity: 'Common',
      description: '每日体力恢复 +10',
      effects: [
        {
          type: 'stamina_recovery',
          target: 'daily',
          value: 10,
          description: '每日额外恢复10点体力'
        }
      ]
    },
    {
      id: 'spirit_fruit_planting',
      name: '灵果种植',
      rarity: 'Common',
      description: '农场种植成功率提升，灵果成熟提前1天',
      effects: [
        {
          type: 'special',
          target: 'farm',
          value: 1,
          description: '农场种植成功率提升，灵果成熟时间减少1天'
        }
      ]
    },

    // 稀有技能
    {
      id: 'milk_mastery',
      name: '乳液专精',
      rarity: 'Rare',
      description: '乳液生产效率提升 15%，失败率降低',
      effects: [
        {
          type: 'production_efficiency',
          target: 'milk',
          value: 15,
          description: '乳液工坊生产效率提升15%'
        },
        {
          type: 'failure_reduction',
          target: 'milk',
          value: 10,
          description: '乳液工坊失败率降低10%'
        }
      ]
    },
    {
      id: 'nectar_mastery',
      name: '琼浆专精',
      rarity: 'Rare',
      description: '琼浆生产效率提升 15%，失败率降低',
      effects: [
        {
          type: 'production_efficiency',
          target: 'nectar',
          value: 15,
          description: '琼浆工坊生产效率提升15%'
        },
        {
          type: 'failure_reduction',
          target: 'nectar',
          value: 10,
          description: '琼浆工坊失败率降低10%'
        }
      ]
    },
    {
      id: 'needlework_mastery',
      name: '女红精通',
      rarity: 'Rare',
      description: '所有工坊效率 +10%，失败时产物不消失',
      effects: [
        {
          type: 'production_efficiency',
          target: 'all',
          value: 10,
          description: '所有工坊生产效率提升10%'
        },
        {
          type: 'special',
          target: 'all',
          value: 1,
          description: '失败时产物不消失'
        }
      ]
    },
    {
      id: 'irresistible_charm',
      name: '魅力无双',
      rarity: 'Rare',
      description: '黑市拍卖售价 +25%，稀有角色吸引率提升',
      effects: [
        {
          type: 'income_boost',
          target: 'auction',
          value: 25,
          description: '黑市拍卖售价提升25%'
        },
        {
          type: 'special',
          target: 'recruitment',
          value: 15,
          description: '稀有角色招募概率提升'
        }
      ]
    },
    {
      id: 'moonlight_dual_cultivation',
      name: '月夜双修',
      rarity: 'Rare',
      description: '双修心情值恢复额外 +30%，体力 +20',
      effects: [
        {
          type: 'mood_recovery',
          target: 'dual_cultivation',
          value: 30,
          description: '双修时心情恢复额外提升30%'
        },
        {
          type: 'stamina_recovery',
          target: 'dual_cultivation',
          value: 20,
          description: '双修时体力额外恢复20点'
        }
      ]
    },
    {
      id: 'tranquil_mind',
      name: '心境沉静',
      rarity: 'Rare',
      description: '心情永不低于 30%，即"不会生气"',
      effects: [
        {
          type: 'special',
          target: 'mood',
          value: 30,
          description: '心情值永不低于30点'
        }
      ]
    },
    {
      id: 'valley_fairy_grace',
      name: '谷中仙姿',
      rarity: 'Rare',
      description: '拍卖中高概率刷新稀有少女或珍稀商品',
      effects: [
        {
          type: 'special',
          target: 'auction_refresh',
          value: 25,
          description: '拍卖刷新稀有物品概率提升25%'
        }
      ]
    },
    {
      id: 'fortune_blessed',
      name: '气运加身',
      rarity: 'Rare',
      description: '被招募几率提升 10%，掉落概率 +5%',
      effects: [
        {
          type: 'special',
          target: 'recruitment',
          value: 10,
          description: '被招募概率提升10%'
        },
        {
          type: 'special',
          target: 'drop_rate',
          value: 5,
          description: '掉落概率提升5%'
        }
      ]
    },
    {
      id: 'group_cultivation_leader',
      name: '群修引领',
      rarity: 'Rare',
      description: '群修人数+1上限',
      effects: [
        {
          type: 'special',
          target: 'group_cultivation',
          value: 1,
          description: '群修参与人数上限+1'
        }
      ]
    }
  ])

  // 根据ID获取技能
  function getSkillById(id: string): Skill | undefined {
    return skillDatabase.value.find(skill => skill.id === id)
  }

  // 根据稀有度获取技能列表
  function getSkillsByRarity(rarity: SkillRarity): Skill[] {
    return skillDatabase.value.filter(skill => skill.rarity === rarity)
  }

  // 获取随机技能
  function getRandomSkill(rarity: SkillRarity): Skill | undefined {
    const skills = getSkillsByRarity(rarity)
    if (skills.length === 0) return undefined
    return skills[Math.floor(Math.random() * skills.length)]
  }

  // 计算技能效果
  function calculateSkillEffect(skillIds: string[], effectType: string, target: string): number {
    let totalEffect = 0
    
    skillIds.forEach(skillId => {
      const skill = getSkillById(skillId)
      if (skill) {
        skill.effects.forEach(effect => {
          if (effect.type === effectType && (effect.target === target || effect.target === 'all')) {
            totalEffect += effect.value
          }
        })
      }
    })
    
    return totalEffect
  }

  return {
    skillDatabase,
    getSkillById,
    getSkillsByRarity,
    getRandomSkill,
    calculateSkillEffect
  }
})
