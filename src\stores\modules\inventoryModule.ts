import { ref } from 'vue'

// 材料库存类型
export interface Materials {
  seeds: number          // 种子
  fruits: number         // 灵果
  water: number          // 圣水
  honey: number          // 蜜酿
  milk: number           // 乳液
  nectar: number         // 琼浆
}

// 库存变化记录类型
export interface InventoryChange {
  materialType: keyof Materials
  amount: number
  reason: string
  timestamp: number
  day: number
}

// 库存模块状态
export function createInventoryModule() {
  // 材料库存
  const materials = ref<Materials>({
    seeds: 10,          // 种子
    fruits: 5,          // 灵果
    water: 20,          // 圣水
    honey: 15,          // 蜜酿
    milk: 8,            // 乳液
    nectar: 3,          // 琼浆
  })

  // 库存变化历史记录
  const inventoryHistory = ref<InventoryChange[]>([])

  // 记录库存变化
  function recordInventoryChange(materialType: keyof Materials, amount: number, reason: string, currentDay: number) {
    inventoryHistory.value.push({
      materialType,
      amount,
      reason,
      timestamp: Date.now(),
      day: currentDay
    })
  }

  // 增加材料
  function addMaterial(type: keyof Materials, amount: number, reason: string = '获得材料', currentDay: number) {
    materials.value[type] += amount
    recordInventoryChange(type, amount, reason, currentDay)
  }

  // 消耗材料
  function consumeMaterial(type: keyof Materials, amount: number, reason: string = '消耗材料', currentDay: number): boolean {
    if (materials.value[type] >= amount) {
      materials.value[type] -= amount
      recordInventoryChange(type, -amount, reason, currentDay)
      return true
    }
    return false
  }

  // 批量增加材料
  function addMaterials(materialsToAdd: Partial<Materials>, reason: string = '批量获得材料', currentDay: number) {
    Object.entries(materialsToAdd).forEach(([type, amount]) => {
      if (amount && amount > 0) {
        addMaterial(type as keyof Materials, amount, reason, currentDay)
      }
    })
  }

  // 批量消耗材料
  function consumeMaterials(materialsToConsume: Partial<Materials>, reason: string = '批量消耗材料', currentDay: number): boolean {
    // 先检查是否有足够的材料
    for (const [type, amount] of Object.entries(materialsToConsume)) {
      if (amount && amount > 0) {
        if (materials.value[type as keyof Materials] < amount) {
          return false
        }
      }
    }

    // 如果检查通过，执行消耗
    Object.entries(materialsToConsume).forEach(([type, amount]) => {
      if (amount && amount > 0) {
        consumeMaterial(type as keyof Materials, amount, reason, currentDay)
      }
    })

    return true
  }

  // 检查材料是否足够
  function hasSufficientMaterials(requiredMaterials: Partial<Materials>): boolean {
    return Object.entries(requiredMaterials).every(([type, amount]) => {
      if (!amount || amount <= 0) return true
      return materials.value[type as keyof Materials] >= amount
    })
  }

  // 获取材料总价值（基于基础价格）
  function getMaterialsValue(): number {
    const basePrices: Materials = {
      seeds: 5,
      fruits: 15,
      water: 8,
      honey: 25,
      milk: 30,
      nectar: 100
    }

    return Object.entries(materials.value).reduce((total, [type, amount]) => {
      return total + (amount * basePrices[type as keyof Materials])
    }, 0)
  }

  // 获取指定天数的库存变化记录
  function getDayInventoryChanges(day: number): InventoryChange[] {
    return inventoryHistory.value.filter(change => change.day === day)
  }

  // 获取指定材料的变化记录
  function getMaterialChanges(materialType: keyof Materials): InventoryChange[] {
    return inventoryHistory.value.filter(change => change.materialType === materialType)
  }

  // 获取材料变化统计（指定天数范围）
  function getMaterialStats(materialType: keyof Materials, days: number = 7): {
    totalGained: number
    totalConsumed: number
    netChange: number
  } {
    const recentChanges = inventoryHistory.value
      .filter(change => change.materialType === materialType)
      .slice(-days)

    const totalGained = recentChanges
      .filter(change => change.amount > 0)
      .reduce((sum, change) => sum + change.amount, 0)

    const totalConsumed = Math.abs(recentChanges
      .filter(change => change.amount < 0)
      .reduce((sum, change) => sum + change.amount, 0))

    return {
      totalGained,
      totalConsumed,
      netChange: totalGained - totalConsumed
    }
  }

  // 清理历史记录（保留最近30天）
  function cleanupHistory(currentDay: number) {
    const cutoffDay = currentDay - 30
    inventoryHistory.value = inventoryHistory.value.filter(change => change.day > cutoffDay)
  }

  // 重置库存
  function resetInventory() {
    materials.value = {
      seeds: 10,
      fruits: 5,
      water: 20,
      honey: 15,
      milk: 8,
      nectar: 3,
    }
    inventoryHistory.value = []
  }

  return {
    // 状态
    materials,
    inventoryHistory,

    // 方法
    recordInventoryChange,
    addMaterial,
    consumeMaterial,
    addMaterials,
    consumeMaterials,
    hasSufficientMaterials,
    getMaterialsValue,
    getDayInventoryChanges,
    getMaterialChanges,
    getMaterialStats,
    cleanupHistory,
    resetInventory
  }
}

export type InventoryModule = ReturnType<typeof createInventoryModule>
