import { ref } from 'vue'

// 玩家资源类型
export interface PlayerResources {
  money: number        // 金币
  gems: number         // 灵石
  energy: number       // 体力
  maxEnergy: number    // 最大体力
}

// 玩家数值变化记录类型
export interface PlayerStatsChange {
  type: 'energy' | 'money' | 'gems'
  amount: number
  reason: string
  timestamp: number
  day: number
}

// 玩家数值模块状态
export function createPlayerStatsModule() {
  // 玩家资源
  const playerResources = ref<PlayerResources>({
    money: 1000,        // 金币
    gems: 50,           // 灵石
    energy: 80,         // 体力
    maxEnergy: 100,     // 最大体力
  })

  // 玩家数值变化历史记录
  const playerStatsHistory = ref<PlayerStatsChange[]>([])

  // 记录数值变化
  function recordStatsChange(type: 'energy' | 'money' | 'gems', amount: number, reason: string, currentDay: number) {
    playerStatsHistory.value.push({
      type,
      amount,
      reason,
      timestamp: Date.now(),
      day: currentDay
    })
  }

  // 每日体力恢复
  function restoreEnergyDaily(currentDay: number) {
    const oldEnergy = playerResources.value.energy
    playerResources.value.energy = Math.min(
      playerResources.value.energy + 30,
      playerResources.value.maxEnergy
    )
    const actualRestore = playerResources.value.energy - oldEnergy
    
    if (actualRestore > 0) {
      recordStatsChange('energy', actualRestore, '每日恢复', currentDay)
    }
  }

  // 消耗体力
  function consumeEnergy(amount: number, reason: string = '消耗体力', currentDay: number): boolean {
    if (playerResources.value.energy >= amount) {
      playerResources.value.energy -= amount
      recordStatsChange('energy', -amount, reason, currentDay)
      return true
    }
    return false
  }

  // 恢复体力
  function restoreEnergy(amount: number, reason: string = '恢复体力', currentDay: number) {
    const oldEnergy = playerResources.value.energy
    playerResources.value.energy = Math.min(
      playerResources.value.energy + amount,
      playerResources.value.maxEnergy
    )
    const actualRestore = playerResources.value.energy - oldEnergy
    
    if (actualRestore > 0) {
      recordStatsChange('energy', actualRestore, reason, currentDay)
    }
  }

  // 增加金币
  function addMoney(amount: number, reason: string = '获得金币', currentDay: number) {
    playerResources.value.money += amount
    recordStatsChange('money', amount, reason, currentDay)
  }

  // 消耗金币
  function spendMoney(amount: number, reason: string = '消费金币', currentDay: number): boolean {
    if (playerResources.value.money >= amount) {
      playerResources.value.money -= amount
      recordStatsChange('money', -amount, reason, currentDay)
      return true
    }
    return false
  }

  // 增加灵石
  function addGems(amount: number, reason: string = '获得灵石', currentDay: number) {
    playerResources.value.gems += amount
    recordStatsChange('gems', amount, reason, currentDay)
  }

  // 消耗灵石
  function spendGems(amount: number, reason: string = '消费灵石', currentDay: number): boolean {
    if (playerResources.value.gems >= amount) {
      playerResources.value.gems -= amount
      recordStatsChange('gems', -amount, reason, currentDay)
      return true
    }
    return false
  }

  // 获取指定天数的数值变化记录
  function getDayStatsChanges(day: number): PlayerStatsChange[] {
    return playerStatsHistory.value.filter(change => change.day === day)
  }

  // 获取指定类型的数值变化记录
  function getStatsChangesByType(type: 'energy' | 'money' | 'gems'): PlayerStatsChange[] {
    return playerStatsHistory.value.filter(change => change.type === type)
  }

  // 清理历史记录（保留最近30天）
  function cleanupHistory(currentDay: number) {
    const cutoffDay = currentDay - 30
    playerStatsHistory.value = playerStatsHistory.value.filter(change => change.day > cutoffDay)
  }

  // 重置玩家资源
  function resetPlayerResources() {
    playerResources.value = {
      money: 1000,
      gems: 50,
      energy: 80,
      maxEnergy: 100,
    }
    playerStatsHistory.value = []
  }

  return {
    // 状态
    playerResources,
    playerStatsHistory,

    // 方法
    recordStatsChange,
    restoreEnergyDaily,
    consumeEnergy,
    restoreEnergy,
    addMoney,
    spendMoney,
    addGems,
    spendGems,
    getDayStatsChanges,
    getStatsChangesByType,
    cleanupHistory,
    resetPlayerResources
  }
}

export type PlayerStatsModule = ReturnType<typeof createPlayerStatsModule>
