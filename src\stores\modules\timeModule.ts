import { ref, computed } from 'vue'
import dayjs from 'dayjs'

export type TimeSlot = '上午' | '下午' | '夜晚'

export interface TimeModule {
  // 状态
  currentDay: typeof currentDay
  currentTimeSlot: typeof currentTimeSlot
  gameStartDate: typeof gameStartDate
  
  // 计算属性
  currentGameDate: typeof currentGameDate
  isWeekend: typeof isWeekend
  formattedDate: typeof formattedDate
  dayOfWeekText: typeof dayOfWeekText
  fullTimeDisplay: typeof fullTimeDisplay
  
  // 方法
  advanceToAfternoon: () => { success: boolean; message: string }
  advanceToNight: () => { success: boolean; message: string }
  advanceToNextDay: () => { success: boolean; message: string }
  setDay: (day: number) => void
  setTimeSlot: (timeSlot: TimeSlot) => void
  resetTime: () => void
}

export function createTimeModule(): TimeModule {
  // 游戏时间状态
  const currentDay = ref<number>(1)
  const currentTimeSlot = ref<TimeSlot>('上午')
  
  // 游戏开始日期（2025年6月1日）
  const gameStartDate = ref(dayjs('2025-06-01'))

  // 计算属性：当前游戏日期
  const currentGameDate = computed(() => {
    return gameStartDate.value.add(currentDay.value - 1, 'day')
  })

  // 计算属性：是否为周末
  const isWeekend = computed(() => {
    const dayOfWeek = currentGameDate.value.day()
    return dayOfWeek === 0 || dayOfWeek === 6 // 0=周日, 6=周六
  })

  // 计算属性：格式化的日期显示
  const formattedDate = computed(() => {
    return currentGameDate.value.format('YYYY年MM月DD日')
  })

  // 计算属性：星期显示
  const dayOfWeekText = computed(() => {
    const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    return days[currentGameDate.value.day()]
  })

  // 计算属性：完整的时间显示
  const fullTimeDisplay = computed(() => {
    return `${formattedDate.value} ${dayOfWeekText.value} ${currentTimeSlot.value}`
  })

  // 推进到下午
  function advanceToAfternoon(): { success: boolean; message: string } {
    currentTimeSlot.value = '下午'
    return {
      success: true,
      message: '时间推进到下午'
    }
  }

  // 推进到夜晚
  function advanceToNight(): { success: boolean; message: string } {
    currentTimeSlot.value = '夜晚'
    return {
      success: true,
      message: '时间推进到夜晚，所有派遣任务已完成'
    }
  }

  // 推进到下一天
  function advanceToNextDay(): { success: boolean; message: string } {
    currentDay.value += 1
    currentTimeSlot.value = '上午'
    return {
      success: true,
      message: `新的一天开始了！${formattedDate.value}`
    }
  }

  // 设置天数
  function setDay(day: number) {
    currentDay.value = day
  }

  // 设置时间段
  function setTimeSlot(timeSlot: TimeSlot) {
    currentTimeSlot.value = timeSlot
  }

  // 重置时间
  function resetTime() {
    currentDay.value = 1
    currentTimeSlot.value = '上午'
  }

  return {
    // 状态
    currentDay,
    currentTimeSlot,
    gameStartDate,
    
    // 计算属性
    currentGameDate,
    isWeekend,
    formattedDate,
    dayOfWeekText,
    fullTimeDisplay,
    
    // 方法
    advanceToAfternoon,
    advanceToNight,
    advanceToNextDay,
    setDay,
    setTimeSlot,
    resetTime
  }
}
