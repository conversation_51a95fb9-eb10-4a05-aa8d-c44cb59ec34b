<template>
  <div class="llyzf-container">
    <!-- 顶部导航 -->
    <div class="llyzf-header">
      <a-button type="text" @click="goBack" class="back-btn">
        <template #icon>
          <ArrowLeftOutlined />
        </template>
        返回地图
      </a-button>

      <h1 class="llyzf-title">琉璃胭脂坊</h1>

      <div class="time-info">
        <a-tag color="purple" class="time-tag">
          {{ gameStore.fullTimeDisplay }}
        </a-tag>
      </div>
    </div>

    <!-- 工坊选择区域 -->
    <div class="workshop-grid">
      <!-- 会客大厅 -->
      <div class="workshop-card" @click="enterWorkshop('hall')">
        <div class="workshop-icon">🎭</div>
        <h3>会客大厅</h3>
        <p>歌舞曲艺表演</p>
        <div class="workshop-info">
          <span class="slot-info">可派遣: {{ hallSlots }}/{{ maxHallSlots }}</span>
        </div>
      </div>

      <!-- 圣水工坊 -->
      <div class="workshop-card" @click="enterWorkshop('holy-water')">
        <div class="workshop-icon">💧</div>
        <h3>圣水工坊</h3>
        <p>生产圣水</p>
        <div class="workshop-info">
          <span class="slot-info">可派遣: {{ holyWaterSlots }}/{{ maxProductionSlots }}</span>
          <span class="material-cost">消耗材料</span>
        </div>
      </div>

      <!-- 蜜酿工坊 -->
      <div class="workshop-card" @click="enterWorkshop('honey')">
        <div class="workshop-icon">🍯</div>
        <h3>蜜酿工坊</h3>
        <p>生产蜜酿</p>
        <div class="workshop-info">
          <span class="slot-info">可派遣: {{ honeySlots }}/{{ maxProductionSlots }}</span>
          <span class="material-cost">消耗材料</span>
        </div>
      </div>

      <!-- 乳液工坊 -->
      <div class="workshop-card" :class="{ disabled: !milkWorkshopUnlocked }" @click="enterWorkshop('milk')">
        <div class="workshop-icon">🥛</div>
        <h3>乳液工坊</h3>
        <p>生产乳液</p>
        <div class="workshop-info" v-if="milkWorkshopUnlocked">
          <span class="slot-info">可派遣: {{ milkSlots }}/{{ maxProductionSlots }}</span>
          <span class="material-cost">消耗材料</span>
        </div>
        <div class="unlock-info" v-else>
          <span>需要完成章节2并点亮发展树</span>
        </div>
      </div>

      <!-- 琼浆工坊 -->
      <div class="workshop-card" :class="{ disabled: !nectarWorkshopUnlocked }" @click="enterWorkshop('nectar')">
        <div class="workshop-icon">🍷</div>
        <h3>琼浆工坊</h3>
        <p>生产琼浆</p>
        <div class="workshop-info" v-if="nectarWorkshopUnlocked">
          <span class="slot-info">可派遣: {{ nectarSlots }}/{{ maxProductionSlots }}</span>
          <span class="material-cost">消耗材料</span>
        </div>
        <div class="unlock-info" v-else>
          <span>需要完成章节2并点亮发展树</span>
        </div>
      </div>
    </div>

    <!-- 当前派遣状态 -->
    <div class="dispatch-status" v-if="hasActiveDispatches">
      <h3>当前派遣状态</h3>

      <!-- 工坊分组的派遣汇总 -->
      <div class="workshop-dispatch-summary">
        <div v-for="summary in workshopDispatchSummaries" :key="summary.workshopType" class="workshop-summary-card">
          <div class="workshop-summary-header">
            <span class="workshop-icon">{{ getWorkshopIcon(summary.workshopType) }}</span>
            <h4>{{ getWorkshopName(summary.workshopType) }}</h4>
            <span class="character-count">{{ summary.characterCount }}人派遣中</span>
          </div>

          <div class="summary-content">
            <!-- 预期产出 -->
            <div class="expected-output" v-if="summary.totalExpectedOutput">
              <h5>预期产出</h5>
              <div class="output-items">
                <span v-if="summary.totalExpectedOutput.money" class="output-item money">
                  💰 +{{ summary.totalExpectedOutput.money }}
                </span>
                <template v-if="summary.totalExpectedOutput.materials">
                  <span v-for="(amount, material) in summary.totalExpectedOutput.materials"
                        :key="material" class="output-item material">
                    {{ material }} +{{ amount }}
                  </span>
                </template>
              </div>
            </div>

            <!-- 属性消耗 -->
            <div class="character-cost" v-if="summary.totalCharacterCost">
              <h5>总消耗</h5>
              <div class="cost-items">
                <span class="cost-item">体力 -{{ summary.totalCharacterCost.stamina }}</span>
                <span class="cost-item">心情 -{{ summary.totalCharacterCost.mood }}</span>
              </div>
            </div>

            <!-- 派遣角色列表 -->
            <div class="dispatched-characters">
              <h5>派遣角色</h5>
              <div class="character-list">
                <div v-for="character in getWorkshopDispatchedCharacters(summary.workshopType)"
                     :key="character.id" class="character-item">
                  <a-avatar :src="getCharacterPortraitUrl(character.portrait)" :size="32"
                           @click="showCharacterDetails(character)">
                    {{ character.name?.charAt(0) || '?' }}
                  </a-avatar>
                  <span class="character-name" @click="showCharacterDetails(character)">{{ character.name || '未知角色' }}</span>
                  <a-button size="small" type="text" @click="recallCharacter(character.id)">
                    召回
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { ArrowLeftOutlined } from '@ant-design/icons-vue'
import { useGameStore } from '@/stores/gameStore'
import { useCharacterStore } from '@/stores/characterStore'

const router = useRouter()
const gameStore = useGameStore()
const characterStore = useCharacterStore()

// 工坊最大槽位配置
const maxHallSlots = ref(2) // 会客大厅最大槽位
const maxProductionSlots = ref(2) // 生产工坊最大槽位

// 计算各工坊当前派遣的角色数量
const hallSlots = computed(() => {
  return characterStore.characters.filter(char =>
    char.attributes.isAssigned &&
    char.attributes.assignedWorkshop === 'hall'
  ).length
})

const holyWaterSlots = computed(() => {
  return characterStore.characters.filter(char =>
    char.attributes.isAssigned &&
    char.attributes.assignedWorkshop === 'holy-water'
  ).length
})

const honeySlots = computed(() => {
  return characterStore.characters.filter(char =>
    char.attributes.isAssigned &&
    char.attributes.assignedWorkshop === 'honey'
  ).length
})

const milkSlots = computed(() => {
  return characterStore.characters.filter(char =>
    char.attributes.isAssigned &&
    char.attributes.assignedWorkshop === 'milk'
  ).length
})

const nectarSlots = computed(() => {
  return characterStore.characters.filter(char =>
    char.attributes.isAssigned &&
    char.attributes.assignedWorkshop === 'nectar'
  ).length
})

// 工坊解锁状态（模拟数据）
const milkWorkshopUnlocked = ref(false)
const nectarWorkshopUnlocked = ref(false)

// 当前派遣数据
const activeDispatches = computed(() => {
  return characterStore.characters
    .filter(char => char.attributes.isAssigned && char.attributes.assignedWorkshop)
    .map(char => ({
      id: char.id,
      character: char,
      workshop: char.attributes.assignedWorkshop!
    }))
})

// 计算属性
const hasActiveDispatches = computed(() => {
  return activeDispatches.value.length > 0
})

// 工坊派遣汇总
const workshopDispatchSummaries = computed(() => {
  return gameStore.workshopDispatchSummaries
})

// 获取工坊派遣的角色
const getWorkshopDispatchedCharacters = (workshopType: string) => {
  const characterIds = gameStore.getWorkshopDispatchedCharacters(workshopType)
  return characterIds.map((id: string) => characterStore.getCharacterById(id)).filter((char: any) => char !== undefined)
}

// 获取工坊名称
const getWorkshopName = (workshop: string): string => {
  const names: Record<string, string> = {
    hall: '会客大厅',
    'holy-water': '圣水工坊',
    honey: '蜜酿工坊',
    milk: '乳液工坊',
    nectar: '琼浆工坊'
  }
  return names[workshop] || workshop
}

// 获取工坊图标
const getWorkshopIcon = (workshop: string): string => {
  const icons: Record<string, string> = {
    hall: '🎭',
    'holy-water': '💧',
    honey: '🍯',
    milk: '🥛',
    nectar: '🍷'
  }
  return icons[workshop] || '🏭'
}

// 返回地图
const goBack = () => {
  router.push('/main')
}

// 进入工坊
const enterWorkshop = (workshopType: string) => {
  // 检查解锁状态
  if (workshopType === 'milk' && !milkWorkshopUnlocked.value) {
    message.warning('乳液工坊尚未解锁')
    return
  }

  if (workshopType === 'nectar' && !nectarWorkshopUnlocked.value) {
    message.warning('琼浆工坊尚未解锁')
    return
  }

  // 检查时间段
  if (gameStore.currentTimeSlot !== '上午') {
    message.warning('琉璃胭脂坊只在上午时段开放')
    return
  }

  // 跳转到具体工坊页面
  router.push(`/workshop/${workshopType}`)
}

// 获取角色头像URL
const getCharacterPortraitUrl = (portrait: string | undefined) => {
  // 如果portrait为空，返回默认头像
  if (!portrait) {
    return '/src/assets/graphics/portraits/default.jpg'
  }
  // 如果是完整URL，直接返回
  if (portrait.startsWith('http')) {
    return portrait
  }
  // 否则构建本地路径
  return `/src/assets/graphics/portraits/${portrait}`
}

// 显示角色详情
const showCharacterDetails = (character: any) => {
  // 这里可以添加角色详情弹窗逻辑
  // 暂时使用message显示角色信息
  message.info(`角色：${character.name || '未知'} (等级${character.level || 1})`)
}

// 召回角色
const recallCharacter = (characterId: string) => {
  const success = characterStore.unassignCharacter(characterId, gameStore.currentDay)
  if (success) {
    // 从派遣队列中移除
    gameStore.removeFromDispatchQueue(characterId)
    message.success('角色已召回')
    // 保存数据
    characterStore.saveCharacters()
    gameStore.saveGame()
  } else {
    message.error('召回失败')
  }
}

onMounted(() => {
  // 检查是否为上午时段
  if (gameStore.currentTimeSlot !== '上午') {
    message.warning('琉璃胭脂坊只在上午时段开放，将返回地图')
    setTimeout(() => {
      router.push('/main')
    }, 2000)
    return
  }

  // 初始化角色数据
  try {
    if (!characterStore.loadCharacters()) {
      characterStore.initializeDefaultCharacters(gameStore.currentDay)
    }
    message.success('欢迎来到琉璃胭脂坊')
  } catch (error) {
    console.error('Failed to initialize characters:', error)
    message.error('角色数据初始化失败')
  }
})
</script>

<style scoped>
.llyzf-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.llyzf-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30px;
  background: white;
  padding: 16px 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.back-btn {
  font-size: 16px;
}

.llyzf-title {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  background: linear-gradient(135deg, var(--nectar-purple), var(--nectar-gold));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.time-tag {
  font-size: 14px;
  font-weight: bold;
}

.workshop-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.workshop-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.workshop-card:hover:not(.disabled) {
  transform: translateY(-4px);
  border-color: var(--nectar-purple);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.workshop-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #f5f5f5;
}

.workshop-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.workshop-card h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #333;
}

.workshop-card p {
  margin: 0 0 16px 0;
  color: #666;
  font-size: 14px;
}

.workshop-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.slot-info {
  font-size: 12px;
  color: var(--nectar-purple);
  font-weight: bold;
}

.material-cost {
  font-size: 12px;
  color: #f39c12;
}

.unlock-info {
  font-size: 12px;
  color: #e74c3c;
  font-style: italic;
}

.dispatch-status {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dispatch-status h3 {
  margin: 0 0 16px 0;
  color: #333;
}

.workshop-dispatch-summary {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.workshop-summary-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #e9ecef;
}

.workshop-summary-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #dee2e6;
}

.workshop-summary-header h4 {
  margin: 0;
  flex: 1;
  color: #333;
}

.character-count {
  font-size: 12px;
  color: #6c757d;
  background: #e9ecef;
  padding: 2px 8px;
  border-radius: 12px;
}

.summary-content {
  display: grid;
  grid-template-columns: 1fr 1fr 2fr;
  gap: 16px;
}

.expected-output h5,
.character-cost h5,
.dispatched-characters h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #495057;
  font-weight: 600;
}

.output-items,
.cost-items {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.output-item {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  background: #d4edda;
  color: #155724;
}

.output-item.money {
  background: #fff3cd;
  color: #856404;
}

.cost-item {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  background: #f8d7da;
  color: #721c24;
}

.character-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.character-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: white;
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.character-item .character-name {
  flex: 1;
  font-size: 12px;
  font-weight: 500;
  color: #333;
}

.active-dispatches {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.dispatch-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.dispatch-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.character-name {
  font-weight: bold;
  color: #333;
}

.workshop-name {
  font-size: 12px;
  color: #666;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .llyzf-container {
    padding: 16px;
  }

  .llyzf-header {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .workshop-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .workshop-card {
    padding: 20px;
  }

  .workshop-icon {
    font-size: 36px;
  }

  .summary-content {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .workshop-summary-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
