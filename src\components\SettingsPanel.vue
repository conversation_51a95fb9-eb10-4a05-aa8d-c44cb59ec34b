<template>
  <div class="settings-panel">
    <a-tabs v-model:activeKey="activeTab" centered>
      <!-- 显示设置 -->
      <a-tab-pane key="display" tab="显示设置">
        <div class="setting-section">
          <h4>主题色彩</h4>
          <div class="theme-colors">
            <div 
              v-for="theme in settingsStore.availableThemeColors" 
              :key="theme.name"
              class="theme-color-item"
              :class="{ active: settingsStore.currentThemeColor === theme.color }"
              @click="settingsStore.setThemeColor(theme.color)"
            >
              <div 
                class="color-preview" 
                :style="{ backgroundColor: theme.color }"
              ></div>
              <span class="color-label">{{ theme.label }}</span>
            </div>
          </div>
        </div>
      </a-tab-pane>
      
      <!-- 音频设置 -->
      <a-tab-pane key="audio" tab="音频设置">
        <div class="setting-section">
          <a-row :gutter="[16, 16]">
            <a-col :span="24">
              <div class="setting-item">
                <span class="setting-label">音效</span>
                <a-switch 
                  v-model:checked="settingsStore.soundEnabled"
                  @change="settingsStore.setSoundEnabled"
                />
              </div>
            </a-col>
            
            <a-col :span="24">
              <div class="setting-item">
                <span class="setting-label">背景音乐</span>
                <a-switch 
                  v-model:checked="settingsStore.musicEnabled"
                  @change="settingsStore.setMusicEnabled"
                />
              </div>
            </a-col>
            
            <a-col :span="24">
              <div class="setting-item">
                <span class="setting-label">音效音量</span>
                <a-slider 
                  v-model:value="soundVolumePercent"
                  :min="0" 
                  :max="100"
                  :disabled="!settingsStore.soundEnabled"
                  @change="onSoundVolumeChange"
                />
              </div>
            </a-col>
            
            <a-col :span="24">
              <div class="setting-item">
                <span class="setting-label">音乐音量</span>
                <a-slider 
                  v-model:value="musicVolumePercent"
                  :min="0" 
                  :max="100"
                  :disabled="!settingsStore.musicEnabled"
                  @change="onMusicVolumeChange"
                />
              </div>
            </a-col>
          </a-row>
        </div>
      </a-tab-pane>
      
      <!-- 游戏设置 -->
      <a-tab-pane key="game" tab="游戏设置">
        <div class="setting-section">
          <a-row :gutter="[16, 16]">
            <a-col :span="24">
              <div class="setting-item">
                <span class="setting-label">自动保存</span>
                <a-switch 
                  v-model:checked="settingsStore.autoSave"
                  @change="settingsStore.setAutoSave"
                />
              </div>
            </a-col>
            
            <a-col :span="24">
              <div class="setting-item">
                <span class="setting-label">语言</span>
                <a-select 
                  v-model:value="settingsStore.language"
                  style="width: 120px"
                  @change="settingsStore.setLanguage"
                >
                  <a-select-option value="zh-CN">简体中文</a-select-option>
                  <a-select-option value="en-US" disabled>English</a-select-option>
                </a-select>
              </div>
            </a-col>
          </a-row>
        </div>
      </a-tab-pane>
    </a-tabs>
    
    <!-- 底部按钮 -->
    <div class="settings-footer">
      <a-space>
        <a-button @click="resetSettings">恢复默认</a-button>
        <a-button type="primary" @click="$emit('close')">确定</a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { useSettingsStore } from '@/stores/settingsStore'

defineEmits<{
  close: []
}>()

const settingsStore = useSettingsStore()
const activeTab = ref('display')

// 音量百分比计算
const soundVolumePercent = computed({
  get: () => Math.round(settingsStore.soundVolume * 100),
  set: (value: number) => settingsStore.setSoundVolume(value / 100)
})

const musicVolumePercent = computed({
  get: () => Math.round(settingsStore.musicVolume * 100),
  set: (value: number) => settingsStore.setMusicVolume(value / 100)
})

// 音量变化处理
const onSoundVolumeChange = (value: number) => {
  settingsStore.setSoundVolume(value / 100)
}

const onMusicVolumeChange = (value: number) => {
  settingsStore.setMusicVolume(value / 100)
}

// 重置设置
const resetSettings = () => {
  settingsStore.resetSettings()
  message.success('设置已恢复默认')
}
</script>

<style scoped>
.settings-panel {
  padding: 20px 0;
}

.setting-section {
  padding: 0 20px;
}

.setting-section h4 {
  margin-bottom: 16px;
  color: #333;
  font-weight: 600;
}

.theme-colors {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 12px;
  margin-bottom: 20px;
}

.theme-color-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.theme-color-item:hover {
  background-color: #f5f5f5;
}

.theme-color-item.active {
  border-color: var(--primary-color);
  background-color: #f0f5ff;
}

.color-preview {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-bottom: 8px;
  border: 2px solid #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.color-label {
  font-size: 12px;
  color: #666;
  text-align: center;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
}

.setting-label {
  font-weight: 500;
  color: #333;
}

.settings-footer {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  text-align: right;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .theme-colors {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .setting-item .ant-slider {
    width: 100%;
  }
}
</style>
