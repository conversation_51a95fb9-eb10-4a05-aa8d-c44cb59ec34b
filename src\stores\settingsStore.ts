import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { generateTheme, themeColors } from '@/config/theme'
import type { ThemeConfig } from 'ant-design-vue/es/config-provider/context'

export const useSettingsStore = defineStore('settings', () => {
  // 当前主题色
  const currentThemeColor = ref<string>(themeColors.purple)
  
  // 音效开关
  const soundEnabled = ref<boolean>(true)
  
  // 音乐开关
  const musicEnabled = ref<boolean>(true)
  
  // 音量设置
  const soundVolume = ref<number>(0.7)
  const musicVolume = ref<number>(0.5)
  
  // 语言设置
  const language = ref<string>('zh-CN')
  
  // 自动保存开关
  const autoSave = ref<boolean>(true)
  
  // 计算属性：当前主题配置
  const currentTheme = computed<ThemeConfig>(() => {
    return generateTheme(currentThemeColor.value)
  })
  
  // 可选的主题色列表
  const availableThemeColors = computed(() => {
    return Object.entries(themeColors).map(([name, color]) => ({
      name,
      color,
      label: getThemeColorLabel(name)
    }))
  })
  
  // 获取主题色标签
  function getThemeColorLabel(colorName: string): string {
    const labels: Record<string, string> = {
      purple: '神秘紫',
      blue: '天空蓝',
      green: '翡翠绿',
      orange: '琥珀橙',
      red: '胭脂红',
      gold: '琼浆金'
    }
    return labels[colorName] || colorName
  }
  
  // 设置主题色
  function setThemeColor(color: string) {
    currentThemeColor.value = color
    saveSettings()
  }
  
  // 设置音效
  function setSoundEnabled(enabled: boolean) {
    soundEnabled.value = enabled
    saveSettings()
  }
  
  // 设置音乐
  function setMusicEnabled(enabled: boolean) {
    musicEnabled.value = enabled
    saveSettings()
  }
  
  // 设置音量
  function setSoundVolume(volume: number) {
    soundVolume.value = Math.max(0, Math.min(1, volume))
    saveSettings()
  }
  
  function setMusicVolume(volume: number) {
    musicVolume.value = Math.max(0, Math.min(1, volume))
    saveSettings()
  }
  
  // 设置语言
  function setLanguage(lang: string) {
    language.value = lang
    saveSettings()
  }
  
  // 设置自动保存
  function setAutoSave(enabled: boolean) {
    autoSave.value = enabled
    saveSettings()
  }
  
  // 保存设置到本地存储
  function saveSettings() {
    const settings = {
      themeColor: currentThemeColor.value,
      soundEnabled: soundEnabled.value,
      musicEnabled: musicEnabled.value,
      soundVolume: soundVolume.value,
      musicVolume: musicVolume.value,
      language: language.value,
      autoSave: autoSave.value,
    }
    localStorage.setItem('nectar-game-settings', JSON.stringify(settings))
  }
  
  // 从本地存储加载设置
  function loadSettings() {
    try {
      const saved = localStorage.getItem('nectar-game-settings')
      if (saved) {
        const settings = JSON.parse(saved)
        currentThemeColor.value = settings.themeColor || themeColors.purple
        soundEnabled.value = settings.soundEnabled ?? true
        musicEnabled.value = settings.musicEnabled ?? true
        soundVolume.value = settings.soundVolume ?? 0.7
        musicVolume.value = settings.musicVolume ?? 0.5
        language.value = settings.language || 'zh-CN'
        autoSave.value = settings.autoSave ?? true
      }
    } catch (error) {
      console.warn('Failed to load settings:', error)
    }
  }
  
  // 重置设置
  function resetSettings() {
    currentThemeColor.value = themeColors.purple
    soundEnabled.value = true
    musicEnabled.value = true
    soundVolume.value = 0.7
    musicVolume.value = 0.5
    language.value = 'zh-CN'
    autoSave.value = true
    saveSettings()
  }
  
  return {
    // 状态
    currentThemeColor,
    soundEnabled,
    musicEnabled,
    soundVolume,
    musicVolume,
    language,
    autoSave,
    
    // 计算属性
    currentTheme,
    availableThemeColors,
    
    // 方法
    setThemeColor,
    setSoundEnabled,
    setMusicEnabled,
    setSoundVolume,
    setMusicVolume,
    setLanguage,
    setAutoSave,
    saveSettings,
    loadSettings,
    resetSettings,
  }
})
