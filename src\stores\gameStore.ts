import { defineStore } from 'pinia'
import { createDispatchModule, type DispatchModule } from './modules/dispatchModule'
import { createPlayerStatsModule, type PlayerStatsModule } from './modules/playerStatsModule'
import { createInventoryModule, type InventoryModule } from './modules/inventoryModule'
import { createProductionQueueModule, type ProductionQueueModule } from './modules/productionQueueModule'
import { createTimeModule, type TimeModule, type TimeSlot } from './modules/timeModule'
import { createGameStateModule, type GameStateModule } from './modules/gameStateModule'

export type { TimeSlot }

export const useGameStore = defineStore('game', () => {
  // 创建模块实例
  const timeModule = createTimeModule()
  const dispatchModule = createDispatchModule()
  const playerStatsModule = createPlayerStatsModule()
  const inventoryModule = createInventoryModule()
  const productionQueueModule = createProductionQueueModule()
  const gameStateModule = createGameStateModule()

  // 推进时间（委托给游戏状态模块）
  function advanceTime(): { success: boolean; message: string } {
    return gameStateModule.advanceTime(
      timeModule,
      dispatchModule,
      playerStatsModule,
      inventoryModule,
      productionQueueModule
    )
  }

  // 便捷方法：委托给模块
  function restoreEnergyDaily() {
    playerStatsModule.restoreEnergyDaily(timeModule.currentDay.value)
  }

  function consumeEnergy(amount: number, reason?: string): boolean {
    return playerStatsModule.consumeEnergy(amount, reason || '消耗体力', timeModule.currentDay.value)
  }

  function restoreEnergy(amount: number, reason?: string) {
    playerStatsModule.restoreEnergy(amount, reason || '恢复体力', timeModule.currentDay.value)
  }

  function addMoney(amount: number, reason?: string) {
    playerStatsModule.addMoney(amount, reason || '获得金币', timeModule.currentDay.value)
  }

  function spendMoney(amount: number, reason?: string): boolean {
    return playerStatsModule.spendMoney(amount, reason || '消费金币', timeModule.currentDay.value)
  }

  function addGems(amount: number, reason?: string) {
    playerStatsModule.addGems(amount, reason || '获得灵石', timeModule.currentDay.value)
  }

  function spendGems(amount: number, reason?: string): boolean {
    return playerStatsModule.spendGems(amount, reason || '消费灵石', timeModule.currentDay.value)
  }

  function addMaterial(type: string, amount: number, reason?: string) {
    inventoryModule.addMaterial(type as any, amount, reason || '获得材料', timeModule.currentDay.value)
  }

  function consumeMaterial(type: string, amount: number, reason?: string): boolean {
    return inventoryModule.consumeMaterial(type as any, amount, reason || '消耗材料', timeModule.currentDay.value)
  }

  // 保存游戏状态（委托给游戏状态模块）
  function saveGame() {
    gameStateModule.saveGame(
      timeModule,
      dispatchModule,
      playerStatsModule,
      inventoryModule,
      productionQueueModule
    )
  }

  // 加载游戏状态（委托给游戏状态模块）
  function loadGame(): boolean {
    return gameStateModule.loadGame(
      timeModule,
      dispatchModule,
      playerStatsModule,
      inventoryModule,
      productionQueueModule
    )
  }

  // 派遣相关便捷方法：委托给派遣模块
  function addToDispatchQueue(characterId: string, workshopType: string, activity: string) {
    dispatchModule.addToDispatchQueue(characterId, workshopType, activity, timeModule.currentDay.value, timeModule.currentTimeSlot.value)
  }

  function removeFromDispatchQueue(characterId: string) {
    dispatchModule.removeFromDispatchQueue(characterId)
  }

  function clearDispatchQueue() {
    dispatchModule.clearDispatchQueue()
  }

  function endAllDispatches() {
    return dispatchModule.endAllDispatches()
  }

  // 产出队列相关便捷方法：委托给产出队列模块
  function addToProductionQueue(item: {
    type: 'material' | 'money' | 'experience'
    characterId?: string
    characterInfo?: {
      name: string
      portrait: string
      type: 'Normal' | 'Rare' | 'Special'
      level: number
    }
    amount: number
    materialType?: string
    source: string
    workshopType?: string
    activity?: string
  }) {
    productionQueueModule.addToProductionQueue(item, timeModule.currentDay.value, timeModule.currentTimeSlot.value)
  }

  // 处理产出队列（在寝室休息时调用）
  function processProductionQueue(): { money: number; materials: Record<string, number>; experience: Record<string, number> } {
    const result = productionQueueModule.processProductionQueue()

    // 应用奖励到实际资源
    if (result.money > 0) {
      addMoney(result.money, '产出队列结算')
    }

    Object.entries(result.materials).forEach(([materialType, amount]) => {
      addMaterial(materialType, amount, '产出队列结算')
    })

    return result
  }

  // 重置游戏状态（委托给游戏状态模块）
  function resetGame() {
    gameStateModule.resetGame(
      timeModule,
      dispatchModule,
      playerStatsModule,
      inventoryModule,
      productionQueueModule
    )
  }

  return {
    // 核心状态（通过时间模块访问）
    currentDay: timeModule.currentDay,
    currentTimeSlot: timeModule.currentTimeSlot,
    gameStartDate: timeModule.gameStartDate,

    // 模块状态（通过模块访问）
    playerResources: playerStatsModule.playerResources,
    materials: inventoryModule.materials,
    dispatchQueue: dispatchModule.dispatchQueue,
    productionQueue: productionQueueModule.productionQueue,

    // 计算属性（通过时间模块访问）
    currentGameDate: timeModule.currentGameDate,
    isWeekend: timeModule.isWeekend,
    formattedDate: timeModule.formattedDate,
    dayOfWeekText: timeModule.dayOfWeekText,
    fullTimeDisplay: timeModule.fullTimeDisplay,

    // 产出队列计算属性
    pendingQueue: productionQueueModule.pendingQueue,
    pendingMoney: productionQueueModule.pendingMoney,
    pendingMaterialsCount: productionQueueModule.pendingMaterialsCount,
    pendingExperienceCount: productionQueueModule.pendingExperienceCount,

    // 核心方法
    advanceTime,
    saveGame,
    loadGame,
    resetGame,

    // 便捷方法（委托给模块）
    restoreEnergyDaily,
    consumeEnergy,
    restoreEnergy,
    addMoney,
    spendMoney,
    addGems,
    spendGems,
    addMaterial,
    consumeMaterial,
    addToDispatchQueue,
    removeFromDispatchQueue,
    clearDispatchQueue,
    endAllDispatches,
    addToProductionQueue,
    processProductionQueue,

    // 模块访问（用于高级功能）
    time: timeModule,
    dispatch: dispatchModule,
    playerStats: playerStatsModule,
    inventory: inventoryModule,
    production: productionQueueModule,
    gameState: gameStateModule,
  }
})
