<template>
  <div class="character-details">
    <!-- 基础信息 -->
    <div class="details-section">
      <h4>基础信息</h4>
      <a-descriptions :column="2" size="small">
        <a-descriptions-item label="姓名">{{ character.name }}</a-descriptions-item>
        <a-descriptions-item label="类型">{{ getTypeText(character.type) }}</a-descriptions-item>
        <a-descriptions-item label="等级">Lv.{{ character.level }}</a-descriptions-item>
        <a-descriptions-item label="经验">{{ character.experience }}/100</a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag :color="getStatusColor(character.status)">
            {{ getStatusText(character.status) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="连续工作">{{ character.restDays }}天</a-descriptions-item>
        <a-descriptions-item label="邂逅日期">{{ formatMeetDate(character.meetDate) }}</a-descriptions-item>
        <a-descriptions-item label="邂逅天数">第{{ character.meetDay }}天</a-descriptions-item>
        <a-descriptions-item label="邂逅天数">第{{ character.meetDay }}天</a-descriptions-item>
      </a-descriptions>
    </div>

    <!-- 属性详情 -->
    <div class="details-section">
      <h4>属性详情</h4>
      <div class="attribute-grid">
        <div class="attribute-item">
          <div class="attribute-label">体力值</div>
          <div class="attribute-bar">
            <a-progress :percent="character.stamina" :stroke-color="getStaminaColor(character.stamina)"
              :show-info="false" size="small" />
            <span class="attribute-value">{{ character.stamina }}/100</span>
          </div>
        </div>

        <div class="attribute-item">
          <div class="attribute-label">心情值</div>
          <div class="attribute-bar">
            <a-progress :percent="character.mood" :stroke-color="getMoodColor(character.mood)" :show-info="false"
              size="small" />
            <span class="attribute-value">{{ character.mood }}/100</span>
          </div>
        </div>

        <div class="attribute-item">
          <div class="attribute-label">能量值</div>
          <div class="attribute-bar">
            <a-progress :percent="(character.attributes.energy / character.attributes.maxEnergy) * 100"
              stroke-color="#52c41a" :show-info="false" size="small" />
            <span class="attribute-value">{{ character.attributes.energy }}/{{ character.attributes.maxEnergy }}</span>
          </div>
        </div>

        <div class="attribute-item">
          <div class="attribute-label">生产效率</div>
          <div class="attribute-value-only">{{ character.attributes.efficiency }}x</div>
        </div>
      </div>
    </div>

    <!-- 技能详情 -->
    <div class="details-section" v-if="character.attributes.skills.length > 0">
      <h4>技能详情</h4>
      <div class="skills-list">
        <div v-for="skillId in character.attributes.skills" :key="skillId" class="skill-item">
          <div class="skill-header">
            <a-tag :color="getSkillColor(skillId)" class="skill-tag">
              {{ getSkillName(skillId) }}
            </a-tag>
            <span class="skill-rarity">{{ getSkillRarity(skillId) }}</span>
          </div>
          <div class="skill-description">{{ getSkillDescription(skillId) }}</div>
          <div class="skill-effects">
            <div v-for="effect in getSkillEffects(skillId)" :key="effect.description" class="skill-effect">
              • {{ effect.description }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 派遣信息 -->
    <div class="details-section">
      <h4>派遣信息</h4>

      <!-- 当前派遣任务 -->
      <div v-if="character.attributes.isAssigned && character.attributes.assignedWorkshop" class="current-dispatch">
        <h5>当前派遣任务</h5>
        <a-descriptions :column="1" size="small">
          <a-descriptions-item label="工坊">{{ getWorkshopName(character.attributes.assignedWorkshop)
          }}</a-descriptions-item>
          <a-descriptions-item label="活动">{{ getActivityText(character.currentActivity) }}</a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag color="processing">进行中</a-tag>
          </a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 上次派遣任务 -->
      <div v-if="lastDispatchRecord" class="last-dispatch">
        <h5>上次派遣任务</h5>
        <a-descriptions :column="1" size="small">
          <a-descriptions-item label="工坊">{{ getWorkshopName(lastDispatchRecord.workshopType) }}</a-descriptions-item>
          <a-descriptions-item label="活动">{{ getActivityText(lastDispatchRecord.activity) }}</a-descriptions-item>
          <a-descriptions-item label="持续时间">{{ lastDispatchRecord.duration }}天</a-descriptions-item>
          <a-descriptions-item label="开始时间">第{{ lastDispatchRecord.startDay }}天</a-descriptions-item>
          <a-descriptions-item v-if="lastDispatchRecord.endDay" label="结束时间">第{{ lastDispatchRecord.endDay
          }}天</a-descriptions-item>
        </a-descriptions>
      </div>

      <!-- 派遣历史 -->
      <div v-if="character.dispatchHistory.length > 0" class="dispatch-history">
        <h5>派遣历史 (最近5次)</h5>
        <div class="history-list">
          <div v-for="(record, index) in recentDispatchHistory" :key="index" class="history-item">
            <div class="history-header">
              <span class="workshop-name">{{ getWorkshopName(record.workshopType) }}</span>
              <span class="duration">{{ record.duration }}天</span>
            </div>
            <div class="history-details">
              <span class="activity">{{ getActivityText(record.activity) }}</span>
              <span class="date-range">第{{ record.startDay }}天{{ record.endDay ? ` - 第${record.endDay}天` : ' (进行中)'
              }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 无派遣历史 -->
      <div v-if="character.dispatchHistory.length === 0 && !character.attributes.isAssigned" class="no-dispatch">
        <a-empty description="暂无派遣记录" size="small" />
      </div>
    </div>

    <!-- 状态说明 -->
    <div class="details-section">
      <h4>状态说明</h4>
      <div class="status-info">
        <div class="status-item">
          <a-tag color="green">正常</a-tag>
          <span>体力 & 心情 > 50，连续休息</span>
        </div>
        <div class="status-item">
          <a-tag color="orange">疲惫</a-tag>
          <span>连续派遣2天未休息 or 心情或体力低于50</span>
        </div>
        <div class="status-item">
          <a-tag color="red">过劳</a-tag>
          <span>连续派遣3天以上 or 心情/体力低于30</span>
        </div>
        <div class="status-item">
          <a-tag color="blue">休息</a-tag>
          <span>回归宗门修养恢复</span>
        </div>
        <div class="status-item">
          <a-tag color="default">流放</a-tag>
          <span>连续过劳或未修养超过3天</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { Character, DispatchRecord } from '@/stores/characterStore'
import { useSkillStore } from '@/stores/skillStore'

interface Props {
  character: Character
}

const props = defineProps<Props>()
const skillStore = useSkillStore()

// 计算属性：上次派遣记录
const lastDispatchRecord = computed((): DispatchRecord | null => {
  const history = props.character.dispatchHistory
  if (history.length === 0) return null

  // 找到最后一个已完成的派遣记录
  const completedRecords = history.filter(record => record.endDay)
  if (completedRecords.length === 0) return null

  return completedRecords[completedRecords.length - 1]
})

// 计算属性：最近的派遣历史（最多5条）
const recentDispatchHistory = computed((): DispatchRecord[] => {
  return props.character.dispatchHistory
    .slice(-5)
    .reverse() // 最新的在前面
})

// 获取状态文本
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    'Normal': '正常',
    'Tired': '疲惫',
    'Overworked': '过劳',
    'Resting': '休息',
    'Exiled': '流放'
  }
  return statusMap[status] || status
}

// 获取状态颜色
const getStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    'Normal': 'green',
    'Tired': 'orange',
    'Overworked': 'red',
    'Resting': 'blue',
    'Exiled': 'default'
  }
  return colorMap[status] || 'default'
}

// 获取类型文本
const getTypeText = (type: string): string => {
  const typeMap: Record<string, string> = {
    'Normal': '普通',
    'Rare': '稀有',
    'Special': '特殊'
  }
  return typeMap[type] || type
}

// 获取体力颜色
const getStaminaColor = (stamina: number): string => {
  if (stamina >= 70) return '#52c41a'
  if (stamina >= 40) return '#faad14'
  return '#ff4d4f'
}

// 获取心情颜色
const getMoodColor = (mood: number): string => {
  if (mood >= 70) return '#1890ff'
  if (mood >= 40) return '#faad14'
  return '#ff4d4f'
}

// 获取技能名称
const getSkillName = (skillId: string): string => {
  const skill = skillStore.getSkillById(skillId)
  return skill ? skill.name : skillId
}

// 获取技能稀有度
const getSkillRarity = (skillId: string): string => {
  const skill = skillStore.getSkillById(skillId)
  return skill ? (skill.rarity === 'Rare' ? '稀有' : '常见') : '未知'
}

// 获取技能描述
const getSkillDescription = (skillId: string): string => {
  const skill = skillStore.getSkillById(skillId)
  return skill ? skill.description : '未知技能'
}

// 获取技能效果
const getSkillEffects = (skillId: string) => {
  const skill = skillStore.getSkillById(skillId)
  return skill ? skill.effects : []
}

// 获取技能颜色
const getSkillColor = (skillId: string): string => {
  const skill = skillStore.getSkillById(skillId)
  if (!skill) return 'default'
  return skill.rarity === 'Rare' ? 'purple' : 'blue'
}

// 获取工坊名称
const getWorkshopName = (workshopType: string): string => {
  const workshopMap: Record<string, string> = {
    'hall': '会客大厅',
    'holy-water': '圣水工坊',
    'honey': '蜜酿工坊',
    'milk': '乳液工坊',
    'nectar': '琼浆工坊'
  }
  return workshopMap[workshopType] || workshopType
}

// 获取活动文本
const getActivityText = (activity: string): string => {
  const activityMap: Record<string, string> = {
    'Idle': '空闲',
    'Producing': '生产中',
    'Performing': '表演中',
    'Resting': '休息中',
    'Training': '训练中'
  }
  return activityMap[activity] || activity
}

// 格式化邂逅日期
const formatMeetDate = (meetDate: string): string => {
  try {
    const date = new Date(meetDate)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  } catch (error) {
    return '未知日期'
  }
}
</script>

<style scoped>
.character-details {
  max-height: 600px;
  overflow-y: auto;
}

.details-section {
  margin-bottom: 24px;
}

.details-section h4 {
  margin-bottom: 12px;
  color: #333;
  font-weight: bold;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.attribute-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.attribute-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.attribute-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.attribute-bar {
  display: flex;
  align-items: center;
  gap: 8px;
}

.attribute-bar .ant-progress {
  flex: 1;
}

.attribute-value {
  font-size: 12px;
  color: #333;
  font-weight: bold;
  min-width: 50px;
  text-align: right;
}

.attribute-value-only {
  font-size: 14px;
  color: #333;
  font-weight: bold;
}

.skills-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.skill-item {
  padding: 12px;
  background: #fafafa;
  border-radius: 8px;
  border-left: 4px solid var(--nectar-purple);
}

.skill-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.skill-tag {
  font-weight: bold;
}

.skill-rarity {
  font-size: 12px;
  color: #666;
}

.skill-description {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}

.skill-effects {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.skill-effect {
  font-size: 12px;
  color: #666;
  padding-left: 8px;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
}

.status-item span {
  color: #666;
}

/* 派遣信息样式 */
.current-dispatch,
.last-dispatch {
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid var(--nectar-purple);
}

.current-dispatch h5,
.last-dispatch h5 {
  margin: 0 0 8px 0;
  color: #333;
  font-weight: bold;
  font-size: 14px;
}

.dispatch-history {
  margin-top: 16px;
}

.dispatch-history h5 {
  margin: 0 0 12px 0;
  color: #333;
  font-weight: bold;
  font-size: 14px;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.history-item {
  padding: 8px 12px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.workshop-name {
  font-weight: bold;
  color: #333;
  font-size: 13px;
}

.duration {
  font-size: 11px;
  color: #666;
  background: #e6f7ff;
  padding: 2px 6px;
  border-radius: 4px;
}

.history-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  color: #666;
}

.activity {
  font-weight: 500;
}

.date-range {
  color: #999;
}

.no-dispatch {
  padding: 20px;
  text-align: center;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .attribute-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .skill-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .status-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
