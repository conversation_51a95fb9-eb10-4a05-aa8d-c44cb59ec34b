import type { ThemeConfig } from 'ant-design-vue/es/config-provider/context'

// 游戏主题配置
export const gameTheme: ThemeConfig = {
  token: {
    // 主色调
    colorPrimary: '#722ed1',
    colorSuccess: '#52c41a',
    colorWarning: '#faad14',
    colorError: '#f5222d',
    colorInfo: '#1890ff',
    
    // 字体
    fontFamily: "'Running Regular', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif",
    fontSize: 14,
    
    // 圆角
    borderRadius: 8,
    borderRadiusLG: 12,
    borderRadiusSM: 6,
    
    // 间距
    padding: 16,
    paddingLG: 24,
    paddingSM: 12,
    paddingXS: 8,
    
    // 阴影
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
    boxShadowSecondary: '0 2px 8px rgba(0, 0, 0, 0.06)',
    
    // 背景色
    colorBgContainer: '#ffffff',
    colorBgElevated: '#ffffff',
    colorBgLayout: '#f0f2f5',
  },
  components: {
    Button: {
      borderRadius: 8,
      controlHeight: 40,
      controlHeightLG: 48,
      controlHeightSM: 32,
    },
    Card: {
      borderRadius: 12,
      paddingLG: 24,
    },
    Layout: {
      siderBg: '#001529',
      triggerBg: '#002140',
      triggerColor: '#fff',
    },
    Menu: {
      darkItemBg: '#001529',
      darkSubMenuItemBg: '#000c17',
      darkItemSelectedBg: '#722ed1',
    },
    Avatar: {
      borderRadius: 8,
    },
    Modal: {
      borderRadius: 12,
    },
    Drawer: {
      borderRadius: 12,
    },
  },
}

// 可选的主题色彩方案
export const themeColors = {
  purple: '#722ed1',
  blue: '#1890ff',
  green: '#52c41a',
  orange: '#fa8c16',
  red: '#f5222d',
  gold: '#d4af37',
}

// 根据主题色生成完整主题配置
export const generateTheme = (primaryColor: string): ThemeConfig => ({
  ...gameTheme,
  token: {
    ...gameTheme.token,
    colorPrimary: primaryColor,
  },
})
